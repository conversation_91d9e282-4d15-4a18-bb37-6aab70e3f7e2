#!/usr/bin/env python3
"""
详细分析短轨迹评分问题，包括实际案例和改进方案
"""

import math
import numpy as np

def analyze_real_case():
    """分析实际案例中的短轨迹问题"""
    print("=== 实际案例分析 ===")
    print("基于 rtk_part005_AJ06993PAJ00115B1_diagnostic.json 的数据:")
    print()
    
    # 实际案例数据
    segments = [
        {"id": 102215, "duration": 8.48, "points": 66, "final_score": 0.865},
        {"id": 102265, "duration": 25.63, "points": 234, "final_score": 0.842}
    ]
    
    print("轨迹段信息:")
    for seg in segments:
        print(f"  ID {seg['id']}: 时长={seg['duration']:.1f}s, 点数={seg['points']}, 评分={seg['final_score']:.3f}")
    
    print()
    print("观察:")
    print("- 短轨迹(8.48s)的评分(0.865)反而比长轨迹(25.63s)的评分(0.842)更高")
    print("- 这说明短轨迹的空间匹配质量可能更好")
    print("- 但如果阈值设为0.9，短轨迹就会被拒绝")
    print()

def simulate_extreme_cases():
    """模拟极端情况下的评分"""
    print("=== 极端情况模拟 ===")
    print()
    
    # 情况1: 2秒完美匹配轨迹
    print("情况1: 2秒完美匹配轨迹")
    print("- 距离误差 < 0.5米")
    print("- 方向完全一致")
    print("- 无任何异常点")
    
    duration = 2
    
    # 传统评分 - 完美匹配
    peak_score = 0.98  # 几乎完美的空间匹配
    stability_score = 1.0  # 100%好匹配点
    duration_ratio = duration / 30  # 相对于30秒最长轨迹
    legacy_score = peak_score * 0.6 + duration_ratio * 0.3 + stability_score * 0.1
    
    # F1评分 - 完美匹配
    spatial_precision = 0.98
    direction_consistency = 1.0
    normalized_duration = min(duration / 20.0, 1.0)
    temporal_recall = math.sqrt(normalized_duration)
    f1_score = 2 * (spatial_precision * temporal_recall) / (spatial_precision + temporal_recall)
    f1_final_score = f1_score * 0.8 + direction_consistency * 0.2
    
    print(f"传统评分: {legacy_score:.3f} ({'✅' if legacy_score >= 0.7 else '❌'})")
    print(f"F1评分: {f1_final_score:.3f} ({'✅' if f1_final_score >= 0.7 else '❌'})")
    print()
    
    # 情况2: 30秒中等质量轨迹
    print("情况2: 30秒中等质量轨迹")
    print("- 距离误差 2-3米")
    print("- 方向基本一致")
    print("- 有一些异常点")
    
    duration = 30
    
    # 传统评分 - 中等质量
    peak_score = 0.75  # 中等空间匹配
    stability_score = 0.70  # 70%好匹配点
    duration_ratio = duration / 30
    legacy_score = peak_score * 0.6 + duration_ratio * 0.3 + stability_score * 0.1
    
    # F1评分 - 中等质量
    spatial_precision = 0.75
    direction_consistency = 0.80
    normalized_duration = min(duration / 20.0, 1.0)
    temporal_recall = math.sqrt(normalized_duration)
    f1_score = 2 * (spatial_precision * temporal_recall) / (spatial_precision + temporal_recall)
    f1_final_score = f1_score * 0.8 + direction_consistency * 0.2
    
    print(f"传统评分: {legacy_score:.3f} ({'✅' if legacy_score >= 0.7 else '❌'})")
    print(f"F1评分: {f1_final_score:.3f} ({'✅' if f1_final_score >= 0.7 else '❌'})")
    print()
    
    print("结论: 完美的2秒轨迹评分低于中等质量的30秒轨迹！")
    print()

def propose_adaptive_scoring():
    """提出自适应评分方案"""
    print("=== 自适应评分方案 ===")
    print()
    
    print("方案: 质量优先的自适应评分")
    print("核心思想: 当空间匹配质量很高时，大幅降低时长权重")
    print()
    
    def adaptive_scoring(peak_score, duration, max_duration, stability_score):
        """自适应评分算法"""
        duration_ratio = min(duration / max_duration, 1.0)
        
        # 质量阈值
        high_quality_threshold = 0.85
        
        if peak_score >= high_quality_threshold:
            # 高质量轨迹：降低时长权重
            quality_bonus = (peak_score - high_quality_threshold) / (1.0 - high_quality_threshold)
            duration_weight = max(0.1, 0.3 - quality_bonus * 0.2)  # 时长权重从0.3降到0.1
            peak_weight = min(0.8, 0.6 + quality_bonus * 0.2)      # 峰值权重从0.6升到0.8
            stability_weight = 0.1
        else:
            # 普通质量轨迹：使用标准权重
            duration_weight = 0.3
            peak_weight = 0.6
            stability_weight = 0.1
        
        final_score = (
            peak_score * peak_weight +
            duration_ratio * duration_weight +
            stability_score * stability_weight
        )
        
        return final_score, peak_weight, duration_weight
    
    print("测试不同情况:")
    print("时长(s) | 峰值分数 | 稳定性 | 峰值权重 | 时长权重 | 最终评分 | 达到0.7阈值")
    print("-" * 80)
    
    test_cases = [
        (2, 0.95, 0.90),   # 短轨迹，高质量
        (5, 0.95, 0.90),   # 短轨迹，高质量
        (2, 0.75, 0.70),   # 短轨迹，中等质量
        (30, 0.75, 0.70),  # 长轨迹，中等质量
        (30, 0.95, 0.90),  # 长轨迹，高质量
    ]
    
    for duration, peak_score, stability_score in test_cases:
        final_score, peak_weight, duration_weight = adaptive_scoring(
            peak_score, duration, 30, stability_score
        )
        threshold_met = "✅" if final_score >= 0.7 else "❌"
        
        print(f"{duration:6d} | {peak_score:8.2f} | {stability_score:6.2f} | "
              f"{peak_weight:8.1f} | {duration_weight:8.1f} | "
              f"{final_score:8.3f} | {threshold_met}")
    
    print()

def propose_tiered_scoring():
    """提出分层评分方案"""
    print("=== 分层评分方案 ===")
    print()
    
    print("方案: 基于时长的分层评分策略")
    print()
    
    def tiered_scoring(peak_score, duration, stability_score, direction_consistency=0.9):
        """分层评分算法"""
        if duration < 5:
            # 超短轨迹：主要看质量，时长权重很低
            final_score = peak_score * 0.8 + stability_score * 0.2
            category = "超短轨迹"
        elif duration < 15:
            # 短轨迹：平衡质量和时长
            duration_ratio = min(duration / 15, 1.0)
            final_score = peak_score * 0.7 + duration_ratio * 0.2 + stability_score * 0.1
            category = "短轨迹"
        else:
            # 长轨迹：更重视时长和稳定性
            duration_ratio = min(duration / 30, 1.0)
            final_score = peak_score * 0.5 + duration_ratio * 0.3 + stability_score * 0.2
            category = "长轨迹"
        
        return final_score, category
    
    print("测试不同时长轨迹:")
    print("时长(s) | 峰值分数 | 稳定性 | 类别     | 最终评分 | 达到0.7阈值")
    print("-" * 70)
    
    test_cases = [
        (2, 0.95, 0.90),
        (3, 0.95, 0.90),
        (8, 0.95, 0.90),
        (12, 0.95, 0.90),
        (20, 0.95, 0.90),
        (30, 0.95, 0.90),
        (2, 0.75, 0.70),   # 低质量短轨迹
        (30, 0.75, 0.70),  # 低质量长轨迹
    ]
    
    for duration, peak_score, stability_score in test_cases:
        final_score, category = tiered_scoring(peak_score, duration, stability_score)
        threshold_met = "✅" if final_score >= 0.7 else "❌"
        
        print(f"{duration:6d} | {peak_score:8.2f} | {stability_score:6.2f} | "
              f"{category:8s} | {final_score:8.3f} | {threshold_met}")
    
    print()

if __name__ == "__main__":
    analyze_real_case()
    simulate_extreme_cases()
    propose_adaptive_scoring()
    propose_tiered_scoring()
