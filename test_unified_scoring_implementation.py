#!/usr/bin/env python3
"""
测试统一评分实施
"""

import sys
import os
import json
from datetime import datetime, timedelta
import numpy as np

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

try:
    from core.simple_distance_matcher import SimpleDistanceMatcher, TrajectorySegment
    from core.data_utils import RTKPoint, PerceptionPoint
except ImportError as e:
    print(f"导入错误: {e}")
    print("请确保在项目根目录运行此脚本")
    sys.exit(1)

class Config:
    """配置类"""
    def __init__(self, config_dict):
        for key, value in config_dict.items():
            setattr(self, key, value)

def create_test_rtk_points():
    """创建测试RTK点"""
    rtk_points = []
    base_time = datetime.now()
    
    # 创建一条直线轨迹，30秒，每秒一个点
    for i in range(30):
        rtk_point = RTKPoint(
            timestamp=base_time + timedelta(seconds=i),
            lat=39.9042 + i * 0.0001,  # 北京附近，向北移动
            lon=116.4074 + i * 0.0001,  # 向东移动
            speed=10.0,  # 10 m/s
            heading=45.0  # 东北方向
        )
        rtk_points.append(rtk_point)
    
    return rtk_points

def create_test_perception_points(scenario="good_start_bad_end"):
    """创建测试感知点"""
    perception_points = []
    base_time = datetime.now()
    
    if scenario == "good_start_bad_end":
        # 前10秒质量好，后20秒质量差
        for i in range(30):
            if i < 10:
                # 前10秒：高质量，距离RTK很近
                lat_offset = np.random.normal(0, 0.00001)  # 约1米误差
                lon_offset = np.random.normal(0, 0.00001)
            else:
                # 后20秒：低质量，距离RTK较远
                lat_offset = np.random.normal(0, 0.00005)  # 约5米误差
                lon_offset = np.random.normal(0, 0.00005)
            
            perception_point = PerceptionPoint(
                timestamp=base_time + timedelta(seconds=i),
                id=1,
                lat=39.9042 + i * 0.0001 + lat_offset,
                lon=116.4074 + i * 0.0001 + lon_offset,
                speed=10.0,
                heading=45.0
            )
            perception_points.append(perception_point)
    
    elif scenario == "short_high_quality":
        # 短轨迹，5秒，高质量
        for i in range(5):
            lat_offset = np.random.normal(0, 0.00001)  # 约1米误差
            lon_offset = np.random.normal(0, 0.00001)
            
            perception_point = PerceptionPoint(
                timestamp=base_time + timedelta(seconds=i),
                id=1,
                lat=39.9042 + i * 0.0001 + lat_offset,
                lon=116.4074 + i * 0.0001 + lon_offset,
                speed=10.0,
                heading=45.0
            )
            perception_points.append(perception_point)
    
    elif scenario == "medium_quality_long":
        # 长轨迹，20秒，中等质量
        for i in range(20):
            lat_offset = np.random.normal(0, 0.00003)  # 约3米误差
            lon_offset = np.random.normal(0, 0.00003)
            
            perception_point = PerceptionPoint(
                timestamp=base_time + timedelta(seconds=i),
                id=1,
                lat=39.9042 + i * 0.0001 + lat_offset,
                lon=116.4074 + i * 0.0001 + lon_offset,
                speed=10.0,
                heading=45.0
            )
            perception_points.append(perception_point)
    
    return perception_points

def load_config():
    """加载配置"""
    try:
        with open('config/unified_config.json', 'r', encoding='utf-8') as f:
            config_data = json.load(f)
        
        # 展平配置结构
        flat_config = {}
        for section, values in config_data.items():
            if isinstance(values, dict):
                flat_config.update(values)
            else:
                flat_config[section] = values
        
        return Config(flat_config)
    except Exception as e:
        print(f"加载配置失败: {e}")
        # 返回默认配置
        return Config({
            'method': 'unified',
            'local_match_thr': 0.7,
            'peak_window_duration': 5.0,
            'segment_duration': 5.0,
            'quality_threshold_high': 0.8,
            'quality_threshold_medium': 0.6,
            'short_trajectory_threshold': 10.0,
            'sampling_rate': 10.0,
            'spatial_decay_distance': 5.0
        })

def test_unified_scoring():
    """测试统一评分"""
    print("=== 统一评分实施测试 ===")
    print()
    
    # 加载配置
    config = load_config()
    print(f"评分方法: {config.method}")
    print()
    
    # 创建RTK点
    rtk_points = create_test_rtk_points()
    print(f"创建了 {len(rtk_points)} 个RTK点")
    
    # 创建匹配器
    try:
        matcher = SimpleDistanceMatcher(config, rtk_points)
        print("匹配器创建成功")
        print()
    except Exception as e:
        print(f"匹配器创建失败: {e}")
        return
    
    # 测试不同场景
    scenarios = [
        ("前好后差长轨迹", "good_start_bad_end"),
        ("短高质量轨迹", "short_high_quality"),
        ("中等质量长轨迹", "medium_quality_long")
    ]
    
    for scenario_name, scenario_type in scenarios:
        print(f"--- 测试场景: {scenario_name} ---")
        
        # 创建感知点
        perception_points = create_test_perception_points(scenario_type)
        print(f"创建了 {len(perception_points)} 个感知点")
        
        # 创建轨迹段
        if perception_points:
            segment = TrajectorySegment(
                id=1,
                points=perception_points,
                start_time=perception_points[0].timestamp,
                end_time=perception_points[-1].timestamp
            )
            
            print(f"轨迹段时长: {segment.duration:.1f}秒")
            
            # 计算评分
            try:
                if hasattr(matcher, 'unified_scorer'):
                    final_score, result = matcher.unified_scorer.unified_scoring(segment, rtk_points)
                    
                    print(f"最终评分: {final_score:.3f}")
                    print(f"评分策略: {result['strategy']}")
                    print(f"峰值质量: {result['peak_quality']:.3f}")
                    print(f"平均质量: {result['avg_quality']:.3f}")
                    print(f"高质量段数: {result['high_quality_segments']}")
                    print(f"高质量覆盖率: {result['quality_coverage']['high']:.1%}")
                    
                    # 判断是否通过阈值
                    threshold = getattr(config, 'local_match_thr', 0.7)
                    status = "✅ 通过" if final_score >= threshold else "❌ 未通过"
                    print(f"阈值判断 (≥{threshold}): {status}")
                    
                else:
                    print("统一评分器未初始化")
                    
            except Exception as e:
                print(f"评分计算失败: {e}")
                import traceback
                traceback.print_exc()
        
        print()

def compare_scoring_methods():
    """对比不同评分方法"""
    print("=== 评分方法对比 ===")
    print()
    
    # 创建RTK点
    rtk_points = create_test_rtk_points()
    
    # 测试场景：前好后差长轨迹
    perception_points = create_test_perception_points("good_start_bad_end")
    segment = TrajectorySegment(
        id=1,
        points=perception_points,
        start_time=perception_points[0].timestamp,
        end_time=perception_points[-1].timestamp
    )
    
    print(f"测试轨迹: 时长={segment.duration:.1f}秒, 点数={len(segment.points)}")
    print()
    
    # 测试不同评分方法
    methods = ['legacy', 'f1_style', 'unified']
    
    for method in methods:
        print(f"--- {method.upper()} 评分方法 ---")
        
        # 创建配置
        config = load_config()
        config.method = method
        
        try:
            matcher = SimpleDistanceMatcher(config, rtk_points)
            
            # 计算评分
            if method == 'unified' and hasattr(matcher, 'unified_scorer'):
                final_score, result = matcher.unified_scorer.unified_scoring(segment, rtk_points)
                print(f"最终评分: {final_score:.3f}")
                print(f"评分策略: {result['strategy']}")
            else:
                # 对于传统和F1方法，使用现有的评分函数
                # 这里简化处理，实际应该调用相应的评分方法
                print(f"最终评分: 待实现")
                
        except Exception as e:
            print(f"评分失败: {e}")
        
        print()

if __name__ == "__main__":
    test_unified_scoring()
    compare_scoring_methods()
