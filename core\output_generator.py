import csv
import json
import os
from datetime import datetime
from typing import List, Dict, Any
import numpy as np

try:
    from .data_utils import RTKPoint, PerceptionPoint
except ImportError:
    from data_utils import RTKPoint, PerceptionPoint

class OutputGenerator:
    """输出生成器：生成匹配CSV和诊断JSON"""
    
    def __init__(self, params):
        self.params = params
    
    def generate_matched_csv(self, rtk_points: List[RTKPoint], 
                           matched_chain: List, 
                           output_path: str,
                           anomalies: Dict[str, Any]):
        """
        生成匹配的CSV文件
        
        Args:
            rtk_points: RTK轨迹点列表
            matched_chain: 匹配的轨迹链
            output_path: 输出文件路径
            anomalies: 异常事件字典
        """
        # 创建输出目录
        os.makedirs(os.path.dirname(output_path), exist_ok=True)
        
        # 构建时间到轨迹段的映射
        time_to_segment = {}
        for seg in matched_chain:
            for point in seg.points:
                time_to_segment[point.timestamp] = seg
        
        # 构建异常标记映射
        anomaly_tags = self._build_anomaly_tags(anomalies)
        
        # 确定有效匹配时间范围
        if matched_chain:
            match_start = min(seg.start_time for seg in matched_chain)
            match_end = max(seg.end_time for seg in matched_chain)
        else:
            match_start = match_end = None
        
        # 生成CSV
        with open(output_path, 'w', newline='', encoding='utf-8') as csvfile:
            fieldnames = [
                'rtk_timestamp', 'rtk_lat', 'rtk_lon', 'rtk_speed', 'rtk_heading',
                'per_timestamp', 'perception_id', 'per_lat', 'per_lon', 'per_speed', 'per_heading',
                'alt_ids', 'distance_error', 'final_score', 'anomaly_tag', 'rtk_valid'
            ]
            writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
            writer.writeheader()
            
            for rtk_point in rtk_points:
                # 检查RTK点是否在有效匹配区间内
                rtk_valid = (match_start is not None and 
                           match_start <= rtk_point.timestamp <= match_end)
                
                # 查找最近的感知点
                matched_seg = None
                best_per_point = None
                min_time_diff = float('inf')
                
                for seg in matched_chain:
                    for per_point in seg.points:
                        time_diff = abs((per_point.timestamp - rtk_point.timestamp).total_seconds())
                        if time_diff < min_time_diff:
                            min_time_diff = time_diff
                            best_per_point = per_point
                            matched_seg = seg
                
                # 构建CSV行
                row = {
                    'rtk_timestamp': rtk_point.timestamp.isoformat(),
                    'rtk_lat': rtk_point.lat,
                    'rtk_lon': rtk_point.lon,
                    'rtk_speed': rtk_point.speed,
                    'rtk_heading': rtk_point.heading,
                    'rtk_valid': rtk_valid
                }
                
                if best_per_point and min_time_diff <= self.params.max_gap:
                    # 有匹配的感知点
                    row.update({
                        'per_timestamp': best_per_point.timestamp.isoformat(),
                        'perception_id': best_per_point.id,
                        'per_lat': best_per_point.lat,
                        'per_lon': best_per_point.lon,
                        'per_speed': best_per_point.speed,
                        'per_heading': best_per_point.heading,
                        'alt_ids': self._get_alt_ids(best_per_point, anomalies),
                        'distance_error': self._calculate_distance_error(rtk_point, best_per_point),
                        'final_score': getattr(matched_seg, 'final_score', 0),
                        'anomaly_tag': anomaly_tags.get(best_per_point.timestamp, '')
                    })
                else:
                    # 没有匹配的感知点（漏检）
                    row.update({
                        'per_timestamp': '',
                        'perception_id': '',
                        'per_lat': '',
                        'per_lon': '',
                        'per_speed': '',
                        'per_heading': '',
                        'alt_ids': '',
                        'distance_error': '',
                        'final_score': '',
                        'anomaly_tag': self._get_missing_tag(rtk_point.timestamp, anomalies)
                    })
                
                writer.writerow(row)
    
    def generate_diagnostic_json(self, anomalies: Dict[str, Any], 
                               output_path: str,
                               matched_chain: List,
                               rtk_points: List[RTKPoint]):
        """
        生成诊断JSON文件（包含增强间隙分析）
        
        Args:
            anomalies: 异常事件字典
            output_path: 输出文件路径
            matched_chain: 匹配的轨迹链
            rtk_points: RTK轨迹点列表
        """
        # 创建输出目录
        os.makedirs(os.path.dirname(output_path), exist_ok=True)
        
        # 构建诊断报告
        diagnostic = {
            'metadata': {
                'generation_time': datetime.now().isoformat(),
                'rtk_points_count': len(rtk_points),
                'matched_segments_count': len(matched_chain),
                'rtk_duration': (rtk_points[-1].timestamp - rtk_points[0].timestamp).total_seconds() if rtk_points else 0,
                'matched_duration': self._calculate_matched_duration(matched_chain),
                'analysis_type': 'enhanced' if 'enhanced_analysis' in anomalies else 'standard'
            },
            'anomalies': {
                'split_events': self._format_split_events(anomalies.get('split_events', [])),
                'id_switches': self._format_id_switches(anomalies.get('id_switches', [])),
                'missing_gaps': self._format_missing_gaps(anomalies.get('missing_gaps', [])),
                'rejected_segments': self._format_rejected_segments(anomalies.get('rejected_segments', []))
            },
            'statistics': {
                'coverage_rate': self._calculate_coverage_rate(matched_chain, rtk_points),
                'split_count': len(anomalies.get('split_events', [])),
                'switch_count': len(anomalies.get('id_switches', [])),
                'missing_gap_count': len(anomalies.get('missing_gaps', [])),
                'total_missing_duration': sum(gap['duration'] for gap in anomalies.get('missing_gaps', [])),
                'avg_match_score': self._calculate_avg_match_score(matched_chain),
                'unique_ids': list(set(seg.id for seg in matched_chain))
            },
            'segments': self._format_segments(matched_chain)
        }
        
        # 如果有增强分析结果，添加到诊断报告中
        if 'enhanced_analysis' in anomalies:
            diagnostic['enhanced_gap_analysis'] = anomalies['enhanced_analysis']
            diagnostic['gap_analysis_summary'] = self._generate_gap_analysis_summary(anomalies['enhanced_analysis'])
        
        # 写入JSON文件
        with open(output_path, 'w', encoding='utf-8') as f:
            json.dump(diagnostic, f, indent=2, ensure_ascii=False, default=str)
    
    def _build_anomaly_tags(self, anomalies: Dict[str, Any]) -> Dict:
        """构建异常标记映射"""
        tags = {}
        
        # 分裂事件
        for event in anomalies.get('split_events', []):
            tags[event['timestamp']] = 'split_confirmed'
        
        # ID切换事件
        for event in anomalies.get('id_switches', []):
            tags[event['timestamp']] = 'switch'
        
        return tags
    
    def _get_alt_ids(self, per_point: PerceptionPoint, anomalies: Dict[str, Any]) -> str:
        """获取分裂簇中的其他ID"""
        for event in anomalies.get('split_events', []):
            if event['timestamp'] == per_point.timestamp:
                alt_ids = [id for id in event['ids'] if id != per_point.id]
                return ','.join(map(str, alt_ids))
        return ''
    
    def _calculate_distance_error(self, rtk_point: RTKPoint, per_point: PerceptionPoint) -> float:
        """计算位置误差"""
        from math import radians, cos, sin, asin, sqrt
        
        # Haversine公式计算距离
        lat1, lon1 = radians(rtk_point.lat), radians(rtk_point.lon)
        lat2, lon2 = radians(per_point.lat), radians(per_point.lon)
        
        dlat = lat2 - lat1
        dlon = lon2 - lon1
        a = sin(dlat/2)**2 + cos(lat1) * cos(lat2) * sin(dlon/2)**2
        c = 2 * asin(sqrt(a))
        r = 6371000  # 地球半径（米）
        
        return round(c * r, 2)
    
    def _get_missing_tag(self, timestamp, anomalies: Dict[str, Any]) -> str:
        """获取漏检标记"""
        for gap in anomalies.get('missing_gaps', []):
            if gap['start_time'] <= timestamp <= gap['end_time']:
                return 'missing'
        return ''
    
    def _calculate_matched_duration(self, matched_chain: List) -> float:
        """计算匹配的总时长"""
        if not matched_chain:
            return 0
        
        total_duration = 0
        for seg in matched_chain:
            total_duration += seg.duration
        return total_duration
    
    def _calculate_coverage_rate(self, matched_chain: List, rtk_points: List[RTKPoint]) -> float:
        """计算覆盖率"""
        if not rtk_points:
            return 0
        
        rtk_duration = (rtk_points[-1].timestamp - rtk_points[0].timestamp).total_seconds()
        matched_duration = self._calculate_matched_duration(matched_chain)
        
        return round(matched_duration / rtk_duration * 100, 2) if rtk_duration > 0 else 0
    
    def _generate_gap_analysis_summary(self, enhanced_analysis: Dict[str, Any]) -> Dict[str, Any]:
        """生成间隙分析摘要"""
        missing_gaps = enhanced_analysis.get('missing_gaps', [])
        id_connection_gaps = enhanced_analysis.get('id_connection_gaps', [])
        internal_jumps = enhanced_analysis.get('internal_time_jumps', [])
        
        # 分析ID连接处的问题
        problematic_connections = [g for g in id_connection_gaps if g.get('is_problematic', False)]
        motion_discontinuous = [g for g in id_connection_gaps 
                              if g.get('motion_analysis', {}).get('is_continuous') == False]
        
        # 分析漏检类型
        missing_gap_types = {}
        for gap in missing_gaps:
            gap_type = gap.get('type', 'unknown')
            if gap_type not in missing_gap_types:
                missing_gap_types[gap_type] = {'count': 0, 'total_duration': 0}
            missing_gap_types[gap_type]['count'] += 1
            missing_gap_types[gap_type]['total_duration'] += gap.get('duration', 0)
        
        # 分析时间跳跃严重程度
        jump_severity = {'high': 0, 'medium': 0, 'low': 0}
        for jump in internal_jumps:
            severity = jump.get('severity', 'low')
            jump_severity[severity] += 1
        
        summary = {
            'overview': {
                'total_missing_gaps': len(missing_gaps),
                'total_missing_duration': sum(gap.get('duration', 0) for gap in missing_gaps),
                'id_connection_gaps': len(id_connection_gaps),
                'internal_time_jumps': len(internal_jumps),
                'analysis_timestamp': datetime.now().isoformat()
            },
            'missing_gap_analysis': {
                'gap_types': missing_gap_types,
                'severity_distribution': self._classify_gaps_by_severity(missing_gaps)
            },
            'id_connection_analysis': {
                'total_connections': len(id_connection_gaps),
                'problematic_connections': len(problematic_connections),
                'motion_discontinuous': len(motion_discontinuous),
                'continuity_rate': (len(id_connection_gaps) - len(motion_discontinuous)) / len(id_connection_gaps) * 100 if id_connection_gaps else 100
            },
            'internal_jump_analysis': {
                'severity_distribution': jump_severity,
                'total_jump_duration': sum(jump.get('jump_duration', 0) for jump in internal_jumps),
                'avg_jump_duration': np.mean([jump.get('jump_duration', 0) for jump in internal_jumps]) if internal_jumps else 0
            },
            'quality_assessment': {
                'overall_quality': self._assess_overall_quality(missing_gaps, id_connection_gaps, internal_jumps),
                'main_issues': self._identify_main_issues(missing_gaps, id_connection_gaps, internal_jumps),
                'recommendations': self._generate_improvement_recommendations(missing_gaps, id_connection_gaps, internal_jumps)
            }
        }
        
        return summary
    
    def _classify_gaps_by_severity(self, missing_gaps: List[Dict]) -> Dict[str, int]:
        """按严重程度分类间隙"""
        severity_count = {'normal': 0, 'minor': 0, 'moderate': 0, 'severe': 0}
        
        for gap in missing_gaps:
            severity = gap.get('severity', 'normal')
            severity_count[severity] += 1
        
        return severity_count
    
    def _assess_overall_quality(self, missing_gaps: List, id_connection_gaps: List, internal_jumps: List) -> str:
        """评估整体质量"""
        issues = 0
        
        # 检查严重漏检
        severe_gaps = [g for g in missing_gaps if g.get('severity') == 'severe']
        if severe_gaps:
            issues += len(severe_gaps)
        
        # 检查运动不连续
        motion_issues = [g for g in id_connection_gaps 
                        if g.get('motion_analysis', {}).get('is_continuous') == False]
        if motion_issues:
            issues += len(motion_issues)
        
        # 检查严重时间跳跃
        severe_jumps = [j for j in internal_jumps if j.get('severity') == 'high']
        if severe_jumps:
            issues += len(severe_jumps)
        
        if issues == 0:
            return 'excellent'
        elif issues <= 2:
            return 'good'
        elif issues <= 5:
            return 'fair'
        else:
            return 'poor'
    
    def _identify_main_issues(self, missing_gaps: List, id_connection_gaps: List, internal_jumps: List) -> List[str]:
        """识别主要问题"""
        issues = []
        
        # 检查漏检问题
        severe_missing = [g for g in missing_gaps if g.get('severity') in ['severe', 'moderate']]
        if severe_missing:
            issues.append(f"检测到{len(severe_missing)}个严重漏检间隙")
        
        # 检查ID连接问题
        problematic_connections = [g for g in id_connection_gaps if g.get('is_problematic', False)]
        if problematic_connections:
            issues.append(f"检测到{len(problematic_connections)}个有问题的ID连接")
        
        # 检查时间跳跃问题
        severe_jumps = [j for j in internal_jumps if j.get('severity') == 'high']
        if severe_jumps:
            issues.append(f"检测到{len(severe_jumps)}个严重时间跳跃")
        
        if not issues:
            issues.append("未检测到明显问题")
        
        return issues
    
    def _generate_improvement_recommendations(self, missing_gaps: List, id_connection_gaps: List, internal_jumps: List) -> List[str]:
        """生成改进建议"""
        recommendations = []
        
        # 基于漏检分析的建议
        severe_missing = [g for g in missing_gaps if g.get('severity') in ['severe', 'moderate']]
        if severe_missing:
            recommendations.append("调整min_missing_gap参数，降低漏检检测阈值")
            recommendations.append("检查感知系统的检测连续性")
        
        # 基于ID连接分析的建议
        motion_issues = [g for g in id_connection_gaps 
                        if g.get('motion_analysis', {}).get('is_continuous') == False]
        if motion_issues:
            recommendations.append("调整ID切换的四阈值参数(switch_dt, switch_dist, switch_speed, switch_heading)")
            recommendations.append("检查感知系统的目标跟踪稳定性")
        
        # 基于时间跳跃分析的建议
        severe_jumps = [j for j in internal_jumps if j.get('severity') == 'high']
        if severe_jumps:
            recommendations.append("检查感知系统的时间同步机制")
            recommendations.append("优化数据传输和处理流程")
        
        if not recommendations:
            recommendations.append("当前轨迹匹配质量良好，无需特别调整")
        
        return recommendations
    
    def _calculate_avg_match_score(self, matched_chain: List) -> float:
        """计算平均匹配分数"""
        if not matched_chain:
            return 0
        
        total_score = sum(getattr(seg, 'final_score', 0) for seg in matched_chain)
        return round(total_score / len(matched_chain), 3)
    
    def _format_split_events(self, split_events: List) -> List[Dict]:
        """格式化分裂事件"""
        formatted = []
        for event in split_events:
            result = {
                'timestamp': event['timestamp'].isoformat() if hasattr(event['timestamp'], 'isoformat') else str(event['timestamp']),
                'ids': event['ids']
            }
            if 'score' in event:
                result['score'] = round(event['score'], 3)
            formatted.append(result)
        return formatted
    
    def _format_id_switches(self, id_switches: List) -> List[Dict]:
        """格式化ID切换事件"""
        formatted = []
        for event in id_switches:
            formatted.append({
                'timestamp': event['timestamp'].isoformat() if hasattr(event['timestamp'], 'isoformat') else str(event['timestamp']),
                'from_id': event['from_id'],
                'to_id': event['to_id'],
                'gap_duration': round(event['gap_duration'], 2),
                'distance': round(event['distance'], 2),
                'speed_diff': round(event['speed_diff'], 2),
                'heading_diff': round(event['heading_diff'], 2)
            })
        return formatted
    
    def _format_missing_gaps(self, missing_gaps: List) -> List[Dict]:
        """格式化漏检间隙"""
        formatted = []
        for gap in missing_gaps:
            formatted_gap = {
                'start_time': gap['start_time'].isoformat() if hasattr(gap['start_time'], 'isoformat') else str(gap['start_time']),
                'end_time': gap['end_time'].isoformat() if hasattr(gap['end_time'], 'isoformat') else str(gap['end_time']),
                'duration': round(gap['duration'], 2),
                'type': gap['type']
            }
            
            if 'prev_id' in gap:
                formatted_gap['prev_id'] = gap['prev_id']
            if 'next_id' in gap:
                formatted_gap['next_id'] = gap['next_id']
                
            formatted.append(formatted_gap)
        return formatted
    
    def _format_rejected_segments(self, rejected_segments: List) -> List[Dict]:
        """格式化被拒绝的段"""
        formatted = []
        for seg in rejected_segments:
            formatted.append({
                'timestamp': seg['timestamp'].isoformat() if hasattr(seg['timestamp'], 'isoformat') else str(seg['timestamp']),
                'type': seg['type'],
                'reason': seg['reason']
            })
        return formatted
    
    def _format_segments(self, matched_chain: List) -> List[Dict]:
        """格式化匹配的轨迹段"""
        formatted = []
        for seg in matched_chain:
            formatted.append({
                'id': seg.id,
                'start_time': seg.start_time.isoformat() if hasattr(seg.start_time, 'isoformat') else str(seg.start_time),
                'end_time': seg.end_time.isoformat() if hasattr(seg.end_time, 'isoformat') else str(seg.end_time),
                'duration': round(seg.duration, 2),
                'points_count': len(seg.points),
                'final_score': round(getattr(seg, 'final_score', 0), 3),
                'peak_score': round(getattr(seg, 'peak_score', 0), 3),
                'duration_ratio': round(getattr(seg, 'duration_ratio', 0), 3),
                'stability_weight': round(getattr(seg, 'stability_weight', 0), 3)
            })
        return formatted

def trim_rtk_by_match_time(rtk_points: List[RTKPoint], 
                          matched_chain: List, 
                          buffer_seconds: float = 5.0) -> List[RTKPoint]:
    """
    根据匹配时间范围剪裁RTK轨迹
    
    Args:
        rtk_points: RTK轨迹点列表
        matched_chain: 匹配的轨迹链
        buffer_seconds: 缓冲时间（秒）
        
    Returns:
        剪裁后的RTK轨迹点列表
    """
    if not matched_chain or not rtk_points:
        return rtk_points
    
    # 确定匹配时间范围
    match_start = min(seg.start_time for seg in matched_chain)
    match_end = max(seg.end_time for seg in matched_chain)
    
    # 添加缓冲时间
    from datetime import timedelta
    buffer_delta = timedelta(seconds=buffer_seconds)
    trim_start = match_start - buffer_delta
    trim_end = match_end + buffer_delta
    
    # 剪裁RTK轨迹
    trimmed_points = []
    for point in rtk_points:
        if trim_start <= point.timestamp <= trim_end:
            trimmed_points.append(point)
    
    return trimmed_points 