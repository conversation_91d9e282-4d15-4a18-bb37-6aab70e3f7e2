# 车路协同轨迹匹配系统架构重构方案

## 📋 文档信息
- **创建时间**: 2024年12月
- **版本**: v1.0
- **目标**: 实现系统模块解耦，提高扩展性和可维护性

## 🎯 重构目标

### 1. 核心目标
- **模块解耦**: 各模块职责明确，降低耦合度
- **插件化架构**: 支持动态加载新的数据格式、匹配算法和评价方法
- **配置驱动**: 通过配置文件控制系统行为
- **扩展性**: 便于添加新功能而不影响现有代码

### 2. 解决的问题
- **当前问题**: `DTWMatcher`承担过多职责，难以扩展
- **数据格式限制**: 仅支持特定CSV格式，难以处理其他数据源
- **评价指标固化**: 难以添加新的评价指标
- **配置分散**: 配置参数分散在多个文件中

## 🏗️ 新架构设计

### 1. 整体架构图

```
📁 Standard/
├── 🎯 core/                          # 核心模块
│   ├── 📄 __init__.py
│   ├── 🔧 config_manager.py          # 统一配置管理器
│   ├── 🔄 trajectory_engine.py       # 轨迹匹配引擎 (核心调度)
│   └── 📤 output_manager.py          # 输出管理器
│
├── 📊 data/                          # 数据处理模块 (插件化)
│   ├── 📄 __init__.py
│   ├── 🔧 base_loader.py             # 数据加载器基类
│   ├── 🔧 base_processor.py          # 数据处理器基类
│   ├── loaders/                      # 数据加载器
│   │   ├── 📄 __init__.py
│   │   ├── 📄 csv_loader.py          # CSV文件加载器
│   │   ├── 📄 json_loader.py         # JSON文件加载器
│   │   ├── 📄 nmea_loader.py         # NMEA格式加载器
│   │   ├── 📄 rtcm_loader.py         # RTCM格式加载器
│   │   ├── 📄 bag_loader.py          # ROS Bag加载器
│   │   ├── 📄 protobuf_loader.py     # Protobuf加载器
│   │   ├── 📄 database_loader.py     # 数据库加载器
│   │   └── 📄 mqtt_loader.py         # MQTT实时数据加载器
│   ├── processors/                   # 数据处理器
│   │   ├── 📄 __init__.py
│   │   ├── 📄 time_sync_processor.py # 时间同步处理器
│   │   ├── 📄 coord_transform_processor.py # 坐标转换处理器
│   │   ├── 📄 filter_processor.py    # 数据过滤处理器
│   │   ├── 📄 interpolation_processor.py # 插值处理器
│   │   └── 📄 validation_processor.py # 数据验证处理器
│   └── 📄 data_factory.py            # 数据工厂 (统一入口)
│
├── 🎯 matchers/                      # 匹配器模块 (匹配=评分)
│   ├── 📄 __init__.py
│   ├── 🔧 base_matcher.py            # 匹配器基类
│   ├── 📏 simple_distance_matcher.py # 简单距离匹配器
│   ├── 🌊 dtw_matcher.py            # DTW匹配器 (可选)
│   ├── 🔗 graph_matcher.py          # 图优化匹配器
│   ├── 🤖 ml_matcher.py             # 机器学习匹配器
│   └── 🎯 hybrid_matcher.py         # 混合匹配器
│
├── 📊 evaluators/                    # 评价器模块 (独立评价指标)
│   ├── 📄 __init__.py
│   ├── 🔧 base_evaluator.py         # 评价器基类
│   ├── 🔍 gap_evaluator.py          # 漏检统计评价器
│   ├── 💥 split_evaluator.py        # 分裂统计评价器
│   ├── 🔄 id_switch_evaluator.py    # ID转换统计评价器
│   ├── ↔️ lateral_error_evaluator.py # 横向误差统计评价器
│   ├── ↕️ longitudinal_error_evaluator.py # 纵向误差统计评价器
│   ├── 🎯 accuracy_evaluator.py     # 精度评价器
│   └── 📋 comprehensive_evaluator.py # 综合评价器
│
├── 🔧 utils/                        # 工具模块
│   ├── 📄 __init__.py
│   ├── 🌍 geo_utils.py              # 地理计算工具
│   ├── 🕐 time_utils.py             # 时间处理工具
│   ├── 📊 data_utils.py             # 数据处理工具
│   └── 🔌 plugin_loader.py          # 插件加载器
│
├── ⚙️ config/                       # 配置文件
│   ├── 📄 default.json              # 默认配置
│   ├── 📄 data_profiles.json        # 数据处理配置
│   ├── 📄 matcher_profiles.json     # 匹配器配置文件
│   └── 📄 evaluator_profiles.json   # 评价器配置文件
│
└── 🔌 plugins/                      # 扩展插件目录
    ├── 📄 custom_loader.py          # 自定义加载器示例
    ├── 📄 custom_processor.py       # 自定义处理器示例
    ├── 📄 custom_matcher.py         # 自定义匹配器示例
    └── 📄 custom_evaluator.py       # 自定义评价器示例
```

### 2. 核心概念澄清

#### **匹配器 = 评分器**
- 匹配过程就是评分过程，两者是一体的
- 匹配器负责计算轨迹段之间的匹配分数并确定最佳匹配

#### **评价器独立运行**
- 评价器独立分析匹配结果
- 每个评价器专注于特定的评价指标
- 支持并行执行多个评价器

#### **数据处理插件化**
- 支持多种数据格式和协议
- 通过插件机制动态加载数据加载器
- 支持数据处理管道

## 🔧 核心接口设计

### 1. 数据层接口

#### **数据加载器接口 (IDataLoader)**
```python
class IDataLoader:
    """数据加载器基类"""
    
    def can_load(self, file_path: str) -> bool:
        """检查是否能加载指定文件"""
        pass
    
    def load(self, file_path: str, config: dict = None) -> List[DataPoint]:
        """加载数据文件"""
        pass
    
    def get_supported_formats(self) -> List[str]:
        """返回支持的文件格式"""
        pass
    
    def get_name(self) -> str:
        """返回加载器名称"""
        pass
```

#### **数据处理器接口 (IDataProcessor)**
```python
class IDataProcessor:
    """数据处理器基类"""
    
    def process(self, data: List[DataPoint], config: dict = None) -> List[DataPoint]:
        """处理数据"""
        pass
    
    def get_name(self) -> str:
        """返回处理器名称"""
        pass
    
    def get_config_schema(self) -> dict:
        """返回配置模式"""
        pass
```

#### **统一数据结构**
```python
@dataclass
class DataPoint:
    """统一的数据点结构"""
    timestamp: datetime
    lat: float
    lon: float
    speed: float = 0.0
    heading: float = 0.0
    id: str = None
    source: str = None  # 数据源标识
    raw_data: dict = None  # 原始数据
    metadata: dict = None  # 元数据

@dataclass
class DataSet:
    """数据集"""
    points: List[DataPoint]
    data_type: str  # 'rtk' or 'perception'
    source_format: str  # 'csv', 'json', 'nmea', etc.
    metadata: dict = None
```

### 2. 匹配层接口

#### **匹配器接口 (IMatcher)**
```python
class IMatcher:
    """匹配器基类 - 匹配过程就是评分过程"""
    
    def match_trajectories(self, rtk_data: List[DataPoint], 
                          perception_data: List[DataPoint]) -> MatchResult:
        """执行轨迹匹配，返回匹配结果和评分"""
        pass
    
    def get_config_schema(self) -> dict:
        """返回配置模式"""
        pass
    
    def get_name(self) -> str:
        """返回匹配器名称"""
        pass

@dataclass
class MatchResult:
    """匹配结果"""
    matched_chain: List[dict]
    matcher_name: str
    match_score: float
    coverage_ratio: float
    metadata: dict = None
```

### 3. 评价层接口

#### **评价器接口 (IEvaluator)**
```python
class IEvaluator:
    """评价器基类 - 独立的评价指标"""
    
    def evaluate(self, match_result: MatchResult, 
                rtk_data: List[DataPoint],
                perception_data: List[DataPoint]) -> EvaluationResult:
        """评价匹配结果"""
        pass
    
    def get_metrics(self) -> List[str]:
        """返回评价指标列表"""
        pass
    
    def get_name(self) -> str:
        """返回评价器名称"""
        pass

@dataclass
class EvaluationResult:
    """评价结果"""
    evaluator_name: str
    metrics: dict
    details: dict = None
    metadata: dict = None
```

## ⚙️ 配置系统设计

### 1. 配置文件结构

#### **主配置文件 (config/default.json)**
```json
{
  "data": {
    "rtk": {
      "loader": {
        "type": "csv",
        "config": {
          "column_mapping": {
            "timestamp": "time",
            "lat": "latitude",
            "lon": "longitude",
            "speed": "speed_kmh",
            "heading": "heading_deg"
          },
          "time_format": "%Y-%m-%d %H:%M:%S"
        }
      },
      "processors": [
        {
          "name": "time_sync",
          "config": {
            "time_zone": "UTC",
            "target_zone": "Asia/Shanghai"
          }
        },
        {
          "name": "coord_transform",
          "config": {
            "source_crs": "EPSG:4326",
            "target_crs": "EPSG:4326"
          }
        }
      ]
    },
    "perception": {
      "loader": {
        "type": "json",
        "config": {
          "field_mapping": {
            "timestamp": "timestamp",
            "lat": "position.lat",
            "lon": "position.lon",
            "id": "track_id"
          }
        }
      },
      "processors": [
        {
          "name": "time_sync",
          "config": {
            "time_zone": "Asia/Shanghai",
            "target_zone": "Asia/Shanghai"
          }
        }
      ]
    }
  },
  "matcher": {
    "type": "simple_distance",
    "config": {
      "roi_distance": 50.0,
      "min_segment_length": 3,
      "local_match_thr": 0.8,
      "win_sec": 3.0
    }
  },
  "evaluators": [
    {
      "type": "gap_evaluator",
      "config": {
        "min_missing_gap": 0.5,
        "tolerance_multiplier": 2.0
      }
    },
    {
      "type": "split_evaluator",
      "config": {
        "min_split_duration": 1.0
      }
    },
    {
      "type": "id_switch_evaluator",
      "config": {
        "switch_dt": 2.0,
        "switch_dist": 10.0
      }
    },
    {
      "type": "lateral_error_evaluator",
      "config": {
        "reference_path": "rtk_trajectory"
      }
    },
    {
      "type": "longitudinal_error_evaluator",
      "config": {
        "reference_path": "rtk_trajectory"
      }
    }
  ],
  "output": {
    "formats": ["csv", "json"],
    "include_metadata": true,
    "generate_visualizations": false
  }
}
```

#### **数据配置文件 (config/data_profiles.json)**
```json
{
  "profiles": {
    "standard_csv": {
      "rtk": {
        "loader": "csv",
        "column_mapping": {
          "timestamp": "time",
          "lat": "lat",
          "lon": "lon"
        }
      },
      "perception": {
        "loader": "csv",
        "column_mapping": {
          "timestamp": "timestamp",
          "lat": "lat",
          "lon": "lon",
          "id": "id"
        }
      }
    },
    "nmea_json": {
      "rtk": {
        "loader": "nmea",
        "sentence_types": ["GGA", "RMC"]
      },
      "perception": {
        "loader": "json",
        "field_mapping": {
          "timestamp": "time",
          "lat": "pos.lat",
          "lon": "pos.lon"
        }
      }
    }
  }
}
```

## 🚀 实施计划

### 第一阶段：基础架构搭建 (1-2周)

#### **任务清单**
- [ ] 创建新的目录结构
- [ ] 定义核心接口类
- [ ] 实现配置管理器
- [ ] 创建数据工厂基础框架
- [ ] 实现轨迹引擎框架

#### **交付物**
- 新的目录结构
- 核心接口定义文件
- 基础框架代码

### 第二阶段：数据层重构 (2-3周)

#### **任务清单**
- [ ] 实现CSV数据加载器
- [ ] 实现JSON数据加载器
- [ ] 实现NMEA数据加载器
- [ ] 实现基础数据处理器
- [ ] 实现数据工厂完整功能
- [ ] 迁移现有数据处理逻辑

#### **交付物**
- 完整的数据加载和处理系统
- 支持多种数据格式
- 数据处理管道

### 第三阶段：匹配层重构 (2-3周)

#### **任务清单**
- [ ] 重构简单距离匹配器
- [ ] 保留DTW匹配器(可选)
- [ ] 实现匹配器基类
- [ ] 迁移现有匹配逻辑
- [ ] 优化匹配算法

#### **交付物**
- 插件化的匹配器系统
- 重构后的匹配算法
- 匹配器配置系统

### 第四阶段：评价层重构 (2-3周)

#### **任务清单**
- [ ] 实现漏检统计评价器
- [ ] 实现分裂统计评价器
- [ ] 实现ID转换统计评价器
- [ ] 实现横向误差评价器
- [ ] 实现纵向误差评价器
- [ ] 实现综合评价器

#### **交付物**
- 完整的评价器系统
- 各种评价指标实现
- 评价报告生成器

### 第五阶段：集成测试和优化 (1-2周)

#### **任务清单**
- [ ] 系统集成测试
- [ ] 性能优化
- [ ] 文档完善
- [ ] 示例代码编写
- [ ] 向后兼容性测试

#### **交付物**
- 完整的重构系统
- 测试报告
- 使用文档

## 📈 预期收益

### 1. 扩展性提升
- **新数据格式**: 15分钟内可添加新的数据格式支持
- **新匹配算法**: 1小时内可添加新的匹配算法
- **新评价指标**: 30分钟内可添加新的评价指标

### 2. 维护性提升
- **模块独立**: 各模块可独立开发和测试
- **配置驱动**: 通过配置文件调整系统行为
- **代码复用**: 提高代码复用率50%以上

### 3. 性能提升
- **并行处理**: 支持数据处理和评价的并行执行
- **内存优化**: 流式处理大文件，降低内存占用
- **缓存机制**: 智能缓存提高重复处理效率

## 🔧 技术考虑

### 1. 向后兼容性
- 保留现有API接口
- 提供迁移工具
- 逐步替换旧代码

### 2. 性能要求
- 处理10GB数据文件不超过5分钟
- 内存占用不超过2GB
- 支持实时数据处理

### 3. 可扩展性
- 支持第三方插件
- 提供插件开发SDK
- 动态加载机制

## 📋 风险评估

### 1. 技术风险
- **中等风险**: 大规模重构可能引入新的Bug
- **缓解措施**: 分阶段实施，充分测试

### 2. 兼容性风险
- **低风险**: 可能影响现有工具链
- **缓解措施**: 保留向后兼容接口

### 3. 时间风险
- **中等风险**: 重构时间可能超出预期
- **缓解措施**: 分阶段交付，优先核心功能

## 📚 参考资料

### 1. 设计模式
- 工厂模式: 数据加载器工厂
- 策略模式: 匹配算法选择
- 观察者模式: 评价器系统
- 插件模式: 扩展机制

### 2. 架构原则
- 单一职责原则 (SRP)
- 开闭原则 (OCP)
- 依赖倒置原则 (DIP)
- 接口隔离原则 (ISP)

### 3. 技术栈
- Python 3.8+
- pandas, numpy (数据处理)
- pyproj (坐标转换)
- pydantic (数据验证)
- configparser (配置管理)

---

**文档版本**: v1.0  
**最后更新**: 2024年12月  
**下次审核**: 重构完成后 