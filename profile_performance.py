#!/usr/bin/env python3
"""
性能分析脚本 - 分析轨迹匹配工具各模块的执行时间
"""

import sys
import os
import time
import argparse
from pathlib import Path

# 添加core模块到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'core'))

from core.data_utils import DataLoader, Config
from core.config_loader import load_config, LegacyConfig
from core.simple_distance_matcher import SimpleDistanceMatcher
from core.output_generator import OutputGenerator, trim_rtk_by_match_time
from core.preprocessor import RawDataPreprocessor

def time_function(func, *args, **kwargs):
    """测量函数执行时间"""
    start_time = time.time()
    result = func(*args, **kwargs)
    end_time = time.time()
    return result, end_time - start_time

def main():
    parser = argparse.ArgumentParser(description='轨迹匹配工具性能分析')
    parser.add_argument('--rtk', required=True, help='RTK数据文件路径')
    parser.add_argument('--perception', required=True, help='感知数据文件路径')
    parser.add_argument('--config', default='config/unified_config.json', help='配置文件路径')
    parser.add_argument('--output-dir', default='output', help='输出目录')
    
    args = parser.parse_args()
    
    print("=" * 80)
    print("🔍 轨迹匹配工具性能分析")
    print("=" * 80)
    
    total_start = time.time()
    timings = {}
    
    # 0. 预处理阶段
    print("📋 预处理阶段...")
    preprocess_start = time.time()
    
    preprocessor = RawDataPreprocessor()
    rtk_format = preprocessor.detect_file_format(args.rtk)
    perception_format = preprocessor.detect_file_format(args.perception)
    
    rtk_csv_path = args.rtk
    perception_csv_path = args.perception
    
    if rtk_format != 'csv':
        print(f"   预处理RTK文件: {args.rtk}")
        rtk_csv_path = preprocessor.preprocess_rtk_file(args.rtk, 'temp_rtk.csv')
    
    if perception_format != 'csv':
        print(f"   预处理感知文件: {args.perception}")
        perception_csv_path = preprocessor.preprocess_perception_file(args.perception, 'temp_perception.csv')
    
    timings['预处理'] = time.time() - preprocess_start
    print(f"   ⏱️  预处理耗时: {timings['预处理']:.3f}秒")
    
    # 加载配置
    try:
        unified_config = load_config(args.config)
        config = LegacyConfig(unified_config)
    except Exception as e:
        print(f"警告：配置加载失败 {e}，使用默认配置")
        config = Config()
    
    # 1. 数据加载
    print("\n📂 数据加载阶段...")
    data_loader = DataLoader(config)
    
    rtk_points, load_rtk_time = time_function(data_loader.load_rtk_csv, rtk_csv_path)
    perception_points, load_per_time = time_function(data_loader.load_perception_csv, perception_csv_path)
    
    timings['加载RTK'] = load_rtk_time
    timings['加载感知'] = load_per_time
    timings['数据加载总计'] = load_rtk_time + load_per_time
    
    print(f"   RTK轨迹点: {len(rtk_points)} 个")
    print(f"   感知数据点: {len(perception_points)} 个")
    print(f"   ⏱️  RTK加载耗时: {load_rtk_time:.3f}秒")
    print(f"   ⏱️  感知加载耗时: {load_per_time:.3f}秒")
    print(f"   ⏱️  数据加载总耗时: {timings['数据加载总计']:.3f}秒")
    
    # 2. 时间同步
    print("\n🕐 时间同步阶段...")
    sync_start = time.time()
    
    rtk_points = data_loader.sync_rtk_time(rtk_points)
    perception_points = data_loader.sync_perception_time(perception_points)
    
    timings['时间同步'] = time.time() - sync_start
    print(f"   ⏱️  时间同步耗时: {timings['时间同步']:.3f}秒")
    
    # 3. 空间过滤
    print("\n🎯 空间过滤阶段...")
    matcher = SimpleDistanceMatcher(config, rtk_points)
    
    filtered_perception, filter_time = time_function(matcher.filter_perception_points, perception_points)
    
    timings['空间过滤'] = filter_time
    print(f"   过滤前: {len(perception_points)} 点")
    print(f"   过滤后: {len(filtered_perception)} 点")
    print(f"   ⏱️  空间过滤耗时: {filter_time:.3f}秒")
    
    # 4. 轨迹段构建
    print("\n🔗 轨迹段构建阶段...")
    segments, build_time = time_function(matcher.build_segments, filtered_perception)
    
    timings['轨迹段构建'] = build_time
    print(f"   轨迹段数量: {len(segments)} 个")
    print(f"   ⏱️  轨迹段构建耗时: {build_time:.3f}秒")
    
    # 5. 核心链构建
    print("\n⛓️  核心链构建阶段...")
    core_chain, chain_time = time_function(matcher.build_core_chain, segments)
    
    timings['核心链构建'] = chain_time
    print(f"   核心链段数: {len(core_chain)} 个")
    print(f"   ⏱️  核心链构建耗时: {chain_time:.3f}秒")
    
    # 6. 异常检测
    print("\n🚨 异常检测阶段...")
    rtk_start = rtk_points[0].timestamp
    rtk_end = rtk_points[-1].timestamp
    
    anomalies, anomaly_time = time_function(matcher.final_anomaly_summary, core_chain, rtk_start, rtk_end)
    
    timings['异常检测'] = anomaly_time
    print(f"   分裂事件: {len(anomalies['split_events'])} 个")
    print(f"   ID切换: {len(anomalies['id_switches'])} 个")
    print(f"   漏检间隙: {len(anomalies['missing_gaps'])} 个")
    print(f"   ⏱️  异常检测耗时: {anomaly_time:.3f}秒")
    
    # 7. RTK轨迹剪裁
    print("\n✂️  RTK轨迹剪裁阶段...")
    trimmed_rtk, trim_time = time_function(trim_rtk_by_match_time, rtk_points, core_chain, config.rtk_buffer)
    
    timings['轨迹剪裁'] = trim_time
    print(f"   剪裁前: {len(rtk_points)} 点")
    print(f"   剪裁后: {len(trimmed_rtk)} 点")
    print(f"   ⏱️  轨迹剪裁耗时: {trim_time:.3f}秒")
    
    # 8. 输出生成
    print("\n💾 输出生成阶段...")
    output_dir = Path(args.output_dir)
    output_dir.mkdir(parents=True, exist_ok=True)
    
    rtk_name = Path(args.rtk).stem
    per_name = Path(args.perception).stem
    output_prefix = f"{rtk_name}_{per_name}"
    
    matched_csv_path = output_dir / f"{output_prefix}_trajectory_matched.csv"
    diagnostic_json_path = output_dir / f"{output_prefix}_diagnostic.json"
    
    output_generator = OutputGenerator(config)
    
    # 生成CSV
    _, csv_time = time_function(
        output_generator.generate_matched_csv,
        trimmed_rtk, core_chain, str(matched_csv_path), anomalies
    )
    
    # 生成JSON
    _, json_time = time_function(
        output_generator.generate_diagnostic_json,
        anomalies, str(diagnostic_json_path), core_chain, trimmed_rtk
    )
    
    timings['输出CSV'] = csv_time
    timings['输出JSON'] = json_time
    timings['输出生成总计'] = csv_time + json_time
    
    print(f"   ⏱️  CSV生成耗时: {csv_time:.3f}秒")
    print(f"   ⏱️  JSON生成耗时: {json_time:.3f}秒")
    print(f"   ⏱️  输出生成总耗时: {timings['输出生成总计']:.3f}秒")
    
    # 总计时间
    total_time = time.time() - total_start
    timings['总耗时'] = total_time
    
    # 性能分析报告
    print("\n" + "=" * 80)
    print("📊 性能分析报告")
    print("=" * 80)
    
    # 按耗时排序
    sorted_timings = sorted([(k, v) for k, v in timings.items() if not k.endswith('总计') and k != '总耗时'], 
                           key=lambda x: x[1], reverse=True)
    
    print("🏆 各模块耗时排名:")
    for i, (module, duration) in enumerate(sorted_timings, 1):
        percentage = (duration / total_time) * 100
        print(f"   {i:2d}. {module:<12}: {duration:6.3f}秒 ({percentage:5.1f}%)")
    
    print(f"\n⏱️  程序总耗时: {total_time:.3f}秒")
    
    # 耗时分析
    print("\n🔍 耗时分析:")
    max_time_module = max(sorted_timings, key=lambda x: x[1])
    print(f"   • 最耗时模块: {max_time_module[0]} ({max_time_module[1]:.3f}秒)")
    
    if max_time_module[1] > 1.0:
        print(f"   ⚠️  {max_time_module[0]}模块耗时较长，可能需要优化")
    
    # 数据量分析
    print(f"\n📈 数据量分析:")
    print(f"   • RTK点数: {len(rtk_points)}")
    print(f"   • 感知点数: {len(perception_points)}")
    print(f"   • ROI过滤率: {(1 - len(filtered_perception)/len(perception_points))*100:.1f}%")
    print(f"   • 轨迹段数: {len(segments)}")
    print(f"   • 核心链段数: {len(core_chain)}")
    
    # 效率指标
    if total_time > 0:
        rtk_throughput = len(rtk_points) / total_time
        per_throughput = len(perception_points) / total_time
        print(f"\n⚡ 处理效率:")
        print(f"   • RTK处理速度: {rtk_throughput:.1f} 点/秒")
        print(f"   • 感知处理速度: {per_throughput:.1f} 点/秒")

if __name__ == "__main__":
    main() 