# Config.json字段解释与间隙分析总结

## 📋 概述

本文档提供了车路协同感知轨迹匹配系统中config.json配置文件的详细解释，并重点关注ID轨迹连接处的漏检分析和轨迹内部时间跳跃检测。

## 🔧 Config.json 核心字段解释

### 1. 空间过滤参数
| 字段 | 默认值 | 含义 | 影响 |
|------|--------|------|------|
| `roi_long` | 20.0 | ROI纵向范围（米） | 控制前后方向的目标过滤范围 |
| `roi_lat` | 5.0 | ROI横向范围（米） | 控制左右方向的目标过滤范围 |

### 2. DTW匹配参数
| 字段 | 默认值 | 含义 | 影响 |
|------|--------|------|------|
| `win_sec` | 3.0 | DTW时间窗口（秒） | 限制时间对齐范围 |
| `local_match_thr` | 0.8 | 核心链匹配阈值 | 控制轨迹段进入核心链的门槛 |
| `split_match_thr` | 0.7 | 分裂检测阈值 | 判断两个ID是否为同一车辆分裂 |
| `dtw_radius` | 5 | DTW搜索半径 | 平衡计算效率和准确性 |

### 3. 异常检测参数（关键）
| 字段 | 默认值 | 含义 | 影响 |
|------|--------|------|------|
| `switch_dt` | 2.0 | ID切换时间阈值（秒） | 控制ID切换检测的时间窗口 |
| `switch_dist` | 10.0 | ID切换距离阈值（米） | 位置跳跃的最大允许值 |
| `switch_speed` | 5.0 | ID切换速度差阈值（m/s） | 速度变化的最大允许值 |
| `switch_heading` | 30.0 | ID切换航向差阈值（度） | 航向变化的最大允许值 |

### 4. 间隙填充参数
| 字段 | 默认值 | 含义 | 影响 |
|------|--------|------|------|
| `gap_match_thr` | 0.5 | 间隙填充匹配阈值 | 控制间隙填充的质量要求 |
| `max_missing_gap` | 5.0 | 最大漏检间隙（秒） | 超过此时间不尝试填充 |
| `min_missing_gap` | 0.5 | 最小漏检间隙（秒） | 小于此时间不认为是漏检 |

## 🔍 间隙分析工具功能

### 1. ID轨迹连接处漏检分析

#### 关键分析内容：
- **连接处间隙**：不同ID轨迹段之间的时间间隙
- **运动连续性**：位置、速度、航向的连续性检查
- **填充质量**：间隙填充的匹配分数评估

#### 检测方法：
1. 检测ID变化点
2. 计算时间间隙
3. 评估运动连续性（四阈值检查）
4. 判断间隙类型（正常/异常/显著）

#### 分析结果示例：
```
间隙 30:
  • 时长: 0.10秒
  • ID变化: nan → 101835.0
  • 时间段: 2025-02-25T10:04:43+08:00 ~ 2025-02-25T10:04:43.100000+08:00
  • 运动连续性: ✅ 连续
    - 位置跳跃: 0.86m
    - 速度变化: 0.03m/s
    - 航向变化: 0.2°
```

### 2. 轨迹内部时间跳跃检测

#### 检测方法：
1. 按ID分组分析轨迹
2. 计算相邻点时间间隔
3. 检测异常间隔（超过3倍正常间隔）
4. 评估跳跃严重程度

#### 配置参数：
```json
{
  "normal_detection_interval": 0.1,  // 正常检测间隔（秒）
  "min_missing_gap": 0.5,           // 最小漏检间隙（秒）
  "max_missing_gap": 5.0            // 最大漏检间隙（秒）
}
```

### 3. 轨迹连续性分析

#### 分析指标：
- **覆盖率**：数据点密度与预期的比值
- **间隙数量**：超过阈值的时间间隔数
- **时间间隔统计**：平均值、标准差、最值等

#### 分析结果示例：
```
ID 101987.0:
  • 总时长: 61.30秒
  • 数据点数: 614
  • 覆盖率: 100.16%
  • 间隙数量: 0
  • 长间隙数量: 0
  • 平均间隔: 0.100秒
```

## 🎯 关键关注点

### 1. ID轨迹连接处的漏检

**不关注的漏检**：
- 轨迹头部的漏检（head_missing）
- 轨迹尾部的漏检（tail_missing）

**重点关注的漏检**：
- 不同ID轨迹连接处的漏检（middle_missing）
- 运动不连续的ID连接

**判断标准**：
```python
# 四阈值检查
if distance <= switch_dist and \
   speed_diff <= switch_speed and \
   heading_diff <= switch_heading and \
   time_diff <= switch_dt:
    # 运动连续，正常ID切换
else:
    # 运动不连续，可能存在问题
```

### 2. 轨迹内部时间跳跃

**检测逻辑**：
```python
# 异常间隔检测
normal_interval = 0.1  # 正常检测间隔
tolerance = normal_interval * 3  # 3倍容差

if time_diff > tolerance:
    # 检测到时间跳跃
    severity = 'high' if time_diff > min_missing_gap else 'medium'
```

**关注重点**：
- 同一ID内部的时间跳跃
- 跳跃的严重程度（高/中等）
- 跳跃的频率和分布

## 📊 实际案例分析

### 案例：rtk_part005_AJ06993PAJ00062D1

#### 分析结果：
- **ID连接处间隙**：120个（大部分为0.1秒正常间隔）
- **运动不连续**：17个（主要是航向变化过大）
- **轨迹内部跳跃**：0个
- **轨迹连续性**：优秀（覆盖率>100%）

#### 问题诊断：
- ✅ 时间同步良好
- ✅ ID切换正常
- ⚠️ 部分航向数据不稳定
- ✅ 整体轨迹质量高

## 🔧 配置优化建议

### 1. 针对ID连接处漏检

**严格配置**（高精度场景）：
```json
{
  "switch_dt": 1.5,
  "switch_dist": 8.0,
  "switch_speed": 3.0,
  "switch_heading": 20.0,
  "gap_match_thr": 0.6
}
```

**宽松配置**（低质量数据）：
```json
{
  "switch_dt": 3.0,
  "switch_dist": 15.0,
  "switch_speed": 8.0,
  "switch_heading": 45.0,
  "gap_match_thr": 0.4
}
```

### 2. 针对时间跳跃检测

**高频数据**（10Hz）：
```json
{
  "normal_detection_interval": 0.1,
  "min_missing_gap": 0.3,
  "max_missing_gap": 3.0
}
```

**低频数据**（1Hz）：
```json
{
  "normal_detection_interval": 1.0,
  "min_missing_gap": 2.0,
  "max_missing_gap": 10.0
}
```

## 🛠️ 使用工具

### 1. 配置文件解释工具
```bash
# 查看详细字段解释
cat config_fields_explanation.md
```

### 2. 间隙分析工具
```bash
# 分析轨迹间隙
python gap_analysis_tool.py \
  --matched-csv trajectory_matched.csv \
  --diagnostic-json diagnostic.json \
  --output gap_analysis.json
```

### 3. 使用示例
```bash
# 查看使用示例
cat gap_analysis_usage_example.md
```

## 📈 总结

通过详细的config.json字段解释和专门的间隙分析工具，可以：

1. **精确配置**：根据数据特点调整参数
2. **深入分析**：识别ID连接处的问题
3. **质量评估**：量化轨迹连续性
4. **问题诊断**：自动识别常见问题
5. **优化指导**：为系统改进提供依据

这些工具和文档为车路协同感知轨迹匹配系统的优化和调试提供了强有力的支持。 