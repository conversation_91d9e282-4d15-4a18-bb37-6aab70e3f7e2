{"metadata": {"generation_time": "2025-07-29T16:38:47.237091", "rtk_points_count": 292, "matched_segments_count": 2, "rtk_duration": 29.1, "matched_duration": 20.23, "analysis_type": "enhanced"}, "anomalies": {"split_events": [], "id_switches": [{"timestamp": "2025-02-25T10:06:24.279000+08:00", "from_id": 102215, "to_id": 102271, "gap_duration": 4.92, "distance": 0.0, "speed_diff": 0.0, "heading_diff": 0.0}], "missing_gaps": [{"start_time": "2025-02-25T10:04:25.500000+08:00", "end_time": "2025-02-25T10:06:10.881000+08:00", "duration": 105.38, "type": "head_missing", "next_id": 102215}, {"start_time": "2025-02-25T10:06:19.358000+08:00", "end_time": "2025-02-25T10:06:24.279000+08:00", "duration": 4.92, "type": "middle_missing", "prev_id": 102215, "next_id": 102271}, {"start_time": "2025-02-25T10:06:36.032000+08:00", "end_time": "2025-02-25T10:06:39.500000+08:00", "duration": 3.47, "type": "tail_missing", "prev_id": 102271}], "rejected_segments": []}, "statistics": {"coverage_rate": 69.52, "split_count": 0, "switch_count": 1, "missing_gap_count": 3, "total_missing_duration": 113.77000000000001, "avg_match_score": 0.897, "unique_ids": [102215, 102271]}, "segments": [{"id": 102271, "start_time": "2025-02-25T10:06:24.279000+08:00", "end_time": "2025-02-25T10:06:36.032000+08:00", "duration": 11.75, "points_count": 107, "final_score": 0.922, "peak_score": 0, "duration_ratio": 0, "stability_weight": 0}, {"id": 102215, "start_time": "2025-02-25T10:06:10.881000+08:00", "end_time": "2025-02-25T10:06:19.358000+08:00", "duration": 8.48, "points_count": 66, "final_score": 0.872, "peak_score": 0, "duration_ratio": 0, "stability_weight": 0}], "enhanced_gap_analysis": {"missing_gaps": [{"start_time": "2025-02-25 10:04:25.500000+08:00", "end_time": "2025-02-25 10:06:10.881000+08:00", "duration": 105.381, "type": "head_missing", "severity": "severe", "analysis_type": "enhanced", "next_id": 102215}, {"start_time": "2025-02-25 10:06:19.358000+08:00", "end_time": "2025-02-25 10:06:24.279000+08:00", "duration": 4.921, "type": "middle_missing", "severity": "moderate", "analysis_type": "enhanced", "prev_id": 102215, "next_id": 102271, "motion_continuity": false, "motion_analysis": {"is_continuous": false, "distance": 37.01, "speed_diff": 0.0, "heading_diff": 0.1, "time_diff": 4.92, "conditions": {"distance_ok": false, "speed_ok": true, "heading_ok": true, "time_ok": false}, "thresholds": {"distance_threshold": 10.0, "speed_threshold": 5.0, "heading_threshold": 30.0, "time_threshold": 2.0}}}, {"start_time": "2025-02-25 10:06:36.032000+08:00", "end_time": "2025-02-25 10:06:39.500000+08:00", "duration": 3.468, "type": "tail_missing", "severity": "moderate", "analysis_type": "enhanced", "prev_id": 102271}], "id_connection_gaps": [{"gap_duration": 4.921, "prev_id": 102215, "next_id": 102271, "motion_analysis": {"is_continuous": false, "distance": 37.01, "speed_diff": 0.0, "heading_diff": 0.1, "time_diff": 4.92, "conditions": {"distance_ok": false, "speed_ok": true, "heading_ok": true, "time_ok": false}, "thresholds": {"distance_threshold": 10.0, "speed_threshold": 5.0, "heading_threshold": 30.0, "time_threshold": 2.0}}, "is_problematic": true}], "internal_time_jumps": [{"type": "internal_time_jump", "id": "102271", "jump_duration": 0.236, "jump_start_time": "2025-02-25 10:06:34.353000+08:00", "jump_end_time": "2025-02-25 10:06:34.589000+08:00", "expected_interval": 0.1, "actual_interval": 0.236, "severity": "medium", "before_point": {"timestamp": "2025-02-25 10:06:34.353000+08:00", "lat": 30.461489769385057, "lon": 114.12363922073467, "speed": 0.0, "heading": 151.06802368164062}, "after_point": {"timestamp": "2025-02-25 10:06:34.589000+08:00", "lat": 30.461475558982485, "lon": 114.12364777212518, "speed": 0.0, "heading": 151.04904174804688}}, {"type": "internal_time_jump", "id": "102271", "jump_duration": 0.479, "jump_start_time": "2025-02-25 10:06:35.553000+08:00", "jump_end_time": "2025-02-25 10:06:36.032000+08:00", "expected_interval": 0.1, "actual_interval": 0.479, "severity": "medium", "before_point": {"timestamp": "2025-02-25 10:06:35.553000+08:00", "lat": 30.461397899934667, "lon": 114.12369645244758, "speed": 0.0, "heading": 151.19268798828125}, "after_point": {"timestamp": "2025-02-25 10:06:36.032000+08:00", "lat": 30.46136238471445, "lon": 114.12371897381578, "speed": 0.0, "heading": 151.04043579101562}}, {"type": "internal_time_jump", "id": "102215", "jump_duration": 0.236, "jump_start_time": "2025-02-25 10:06:12.361000+08:00", "jump_end_time": "2025-02-25 10:06:12.597000+08:00", "expected_interval": 0.1, "actual_interval": 0.236, "severity": "medium", "before_point": {"timestamp": "2025-02-25 10:06:12.361000+08:00", "lat": 30.462835313451716, "lon": 114.12292375223628, "speed": 0.0, "heading": 229.5077972412109}, "after_point": {"timestamp": "2025-02-25 10:06:12.597000+08:00", "lat": 30.46283139513579, "lon": 114.1229204776446, "speed": 0.0, "heading": 226.94998168945312}}, {"type": "internal_time_jump", "id": "102215", "jump_duration": 1.52, "jump_start_time": "2025-02-25 10:06:14.278000+08:00", "jump_end_time": "2025-02-25 10:06:15.798000+08:00", "expected_interval": 0.1, "actual_interval": 1.52, "severity": "high", "before_point": {"timestamp": "2025-02-25 10:06:14.278000+08:00", "lat": 30.462787205293782, "lon": 114.12289423685962, "speed": 0.0, "heading": 206.2918243408203}, "after_point": {"timestamp": "2025-02-25 10:06:15.798000+08:00", "lat": 30.46273146849773, "lon": 114.1228811503257, "speed": 0.0, "heading": 184.16261291503903}}], "trajectory_continuity": {}, "gap_statistics": {}}, "gap_analysis_summary": {"overview": {"total_missing_gaps": 3, "total_missing_duration": 113.77000000000001, "id_connection_gaps": 1, "internal_time_jumps": 4, "analysis_timestamp": "2025-07-29T16:38:47.237091"}, "missing_gap_analysis": {"gap_types": {"head_missing": {"count": 1, "total_duration": 105.381}, "middle_missing": {"count": 1, "total_duration": 4.921}, "tail_missing": {"count": 1, "total_duration": 3.468}}, "severity_distribution": {"normal": 0, "minor": 0, "moderate": 2, "severe": 1}}, "id_connection_analysis": {"total_connections": 1, "problematic_connections": 1, "motion_discontinuous": 1, "continuity_rate": 0.0}, "internal_jump_analysis": {"severity_distribution": {"high": 1, "medium": 3, "low": 0}, "total_jump_duration": 2.471, "avg_jump_duration": 0.61775}, "quality_assessment": {"overall_quality": "fair", "main_issues": ["检测到3个严重漏检间隙", "检测到1个有问题的ID连接", "检测到1个严重时间跳跃"], "recommendations": ["调整min_missing_gap参数，降低漏检检测阈值", "检查感知系统的检测连续性", "调整ID切换的四阈值参数(switch_dt, switch_dist, switch_speed, switch_heading)", "检查感知系统的目标跟踪稳定性", "检查感知系统的时间同步机制", "优化数据传输和处理流程"]}}}