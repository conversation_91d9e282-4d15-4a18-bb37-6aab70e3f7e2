#!/usr/bin/env python3
"""
统一评分解决方案：兼顾长短轨迹的问题
"""

import math
import numpy as np

class UnifiedTrajectoryScorer:
    """统一轨迹评分器"""
    
    def __init__(self, config=None):
        self.config = config or {
            'peak_window_duration': 5.0,  # 峰值窗口时长（秒）
            'min_peak_threshold': 0.7,    # 最小峰值阈值
            'segment_duration': 5.0,      # 分段时长（秒）
            'quality_threshold_high': 0.8, # 高质量阈值
            'quality_threshold_medium': 0.6, # 中等质量阈值
            'sampling_rate': 10.0,        # 采样率（Hz）
        }
    
    def distance_to_score(self, distance, decay_distance=5.0):
        """距离转换为评分"""
        return math.exp(-distance / decay_distance)
    
    def calculate_peak_quality(self, trajectory_points):
        """计算峰值质量：寻找最佳质量窗口"""
        scores = [self.distance_to_score(d) for d in trajectory_points]
        
        window_size = int(self.config['peak_window_duration'] * self.config['sampling_rate'])
        window_size = min(window_size, len(scores))
        
        if len(scores) < window_size:
            return np.mean(scores), 0, len(scores)-1
        
        best_score = 0
        best_start = 0
        best_end = window_size - 1
        
        for i in range(len(scores) - window_size + 1):
            window_score = np.mean(scores[i:i+window_size])
            if window_score > best_score:
                best_score = window_score
                best_start = i
                best_end = i + window_size - 1
        
        return best_score, best_start, best_end
    
    def calculate_segment_qualities(self, trajectory_points):
        """计算分段质量"""
        scores = [self.distance_to_score(d) for d in trajectory_points]
        segment_size = int(self.config['segment_duration'] * self.config['sampling_rate'])
        
        segments = []
        for i in range(0, len(scores), segment_size):
            segment_scores = scores[i:i+segment_size]
            if len(segment_scores) >= segment_size // 2:
                segment_avg = np.mean(segment_scores)
                segments.append({
                    'start_idx': i,
                    'end_idx': i + len(segment_scores) - 1,
                    'duration': len(segment_scores) / self.config['sampling_rate'],
                    'quality': segment_avg,
                    'start_time': i / self.config['sampling_rate'],
                    'end_time': (i + len(segment_scores) - 1) / self.config['sampling_rate']
                })
        
        return segments
    
    def unified_scoring(self, trajectory_points, duration):
        """统一评分算法"""
        if not trajectory_points:
            return 0.0, {}
        
        # 1. 基础指标计算
        scores = [self.distance_to_score(d) for d in trajectory_points]
        avg_quality = np.mean(scores)
        
        # 2. 峰值质量计算
        peak_quality, peak_start, peak_end = self.calculate_peak_quality(trajectory_points)
        
        # 3. 分段质量计算
        segments = self.calculate_segment_qualities(trajectory_points)
        
        # 4. 高质量段统计
        high_quality_segments = [s for s in segments if s['quality'] >= self.config['quality_threshold_high']]
        medium_quality_segments = [s for s in segments if s['quality'] >= self.config['quality_threshold_medium']]
        
        # 5. 轨迹类型判断和评分策略
        if duration <= 10:
            # 短轨迹：主要看整体质量
            strategy = "short_trajectory"
            if avg_quality >= self.config['quality_threshold_high']:
                final_score = avg_quality * 0.9 + (duration / 10) * 0.1
            else:
                final_score = avg_quality * 0.8 + (duration / 10) * 0.2
                
        elif peak_quality >= self.config['quality_threshold_high']:
            # 长轨迹但有高质量峰值：峰值优先策略
            strategy = "peak_quality_priority"
            peak_weight = 0.7
            avg_weight = 0.2
            duration_weight = 0.1
            
            duration_bonus = min(duration / 30, 1.0) ** 0.5
            final_score = peak_quality * peak_weight + avg_quality * avg_weight + duration_bonus * duration_weight
            
        elif len(high_quality_segments) > 0:
            # 长轨迹有高质量段：分段优先策略
            strategy = "segment_quality_priority"
            best_segment_quality = max(s['quality'] for s in high_quality_segments)
            high_quality_coverage = sum(s['duration'] for s in high_quality_segments) / duration
            
            segment_weight = 0.6
            coverage_weight = 0.2
            avg_weight = 0.2
            
            final_score = (best_segment_quality * segment_weight + 
                          high_quality_coverage * coverage_weight + 
                          avg_quality * avg_weight)
            
        else:
            # 长轨迹无明显高质量段：传统策略
            strategy = "traditional_long_trajectory"
            duration_ratio = min(duration / 30, 1.0)
            final_score = avg_quality * 0.6 + duration_ratio * 0.3 + (len(segments) / 6) * 0.1
        
        # 6. 构建详细结果
        result = {
            'final_score': final_score,
            'strategy': strategy,
            'duration': duration,
            'avg_quality': avg_quality,
            'peak_quality': peak_quality,
            'peak_window': (peak_start / self.config['sampling_rate'], 
                           peak_end / self.config['sampling_rate']),
            'segments': segments,
            'high_quality_segments': len(high_quality_segments),
            'medium_quality_segments': len(medium_quality_segments),
            'quality_coverage': {
                'high': sum(s['duration'] for s in high_quality_segments) / duration if duration > 0 else 0,
                'medium': sum(s['duration'] for s in medium_quality_segments) / duration if duration > 0 else 0
            }
        }
        
        return final_score, result

def test_unified_scoring():
    """测试统一评分方案"""
    print("=== 统一评分方案测试 ===")
    print()
    
    scorer = UnifiedTrajectoryScorer()
    
    # 测试用例
    test_cases = [
        {
            'name': '短高质量轨迹',
            'description': '5秒，距离误差0.5米',
            'trajectory': [0.5 + np.random.normal(0, 0.1) for _ in range(50)],
            'duration': 5.0
        },
        {
            'name': '短中等质量轨迹',
            'description': '3秒，距离误差2米',
            'trajectory': [2.0 + np.random.normal(0, 0.3) for _ in range(30)],
            'duration': 3.0
        },
        {
            'name': '前好后差长轨迹',
            'description': '30秒，前10秒0.5米，后20秒3米',
            'trajectory': ([0.5 + np.random.normal(0, 0.1) for _ in range(100)] + 
                          [3.0 + np.random.normal(0, 0.5) for _ in range(200)]),
            'duration': 30.0
        },
        {
            'name': '前差后好长轨迹',
            'description': '25秒，前15秒3米，后10秒0.5米',
            'trajectory': ([3.0 + np.random.normal(0, 0.5) for _ in range(150)] + 
                          [0.5 + np.random.normal(0, 0.1) for _ in range(100)]),
            'duration': 25.0
        },
        {
            'name': '中间好两端差长轨迹',
            'description': '30秒，两端差中间好',
            'trajectory': ([3.0 + np.random.normal(0, 0.5) for _ in range(50)] + 
                          [0.5 + np.random.normal(0, 0.1) for _ in range(200)] +
                          [3.0 + np.random.normal(0, 0.5) for _ in range(50)]),
            'duration': 30.0
        },
        {
            'name': '整体中等质量长轨迹',
            'description': '20秒，整体2米误差',
            'trajectory': [2.0 + np.random.normal(0, 0.3) for _ in range(200)],
            'duration': 20.0
        }
    ]
    
    print("测试结果:")
    print("轨迹类型           | 时长 | 评分策略           | 最终评分 | 峰值质量 | 平均质量 | 高质量段数")
    print("-" * 100)
    
    for case in test_cases:
        # 确保距离为正值
        trajectory = [max(0.1, d) for d in case['trajectory']]
        
        final_score, result = scorer.unified_scoring(trajectory, case['duration'])
        
        print(f"{case['name']:18s} | {case['duration']:4.1f} | {result['strategy']:18s} | "
              f"{final_score:8.3f} | {result['peak_quality']:8.3f} | "
              f"{result['avg_quality']:8.3f} | {result['high_quality_segments']:10d}")
    
    print()
    
    # 详细分析一个案例
    print("=== 详细分析：前好后差长轨迹 ===")
    trajectory = ([0.5 + np.random.normal(0, 0.1) for _ in range(100)] + 
                  [3.0 + np.random.normal(0, 0.5) for _ in range(200)])
    trajectory = [max(0.1, d) for d in trajectory]
    
    final_score, result = scorer.unified_scoring(trajectory, 30.0)
    
    print(f"最终评分: {final_score:.3f}")
    print(f"评分策略: {result['strategy']}")
    print(f"峰值质量: {result['peak_quality']:.3f} (时间窗口: {result['peak_window'][0]:.1f}s - {result['peak_window'][1]:.1f}s)")
    print(f"平均质量: {result['avg_quality']:.3f}")
    print(f"高质量段覆盖率: {result['quality_coverage']['high']:.1%}")
    print(f"中等质量段覆盖率: {result['quality_coverage']['medium']:.1%}")
    print()
    
    print("分段质量详情:")
    for i, seg in enumerate(result['segments']):
        quality_level = ("优秀" if seg['quality'] >= 0.8 else 
                        "良好" if seg['quality'] >= 0.6 else 
                        "一般" if seg['quality'] >= 0.4 else "较差")
        print(f"第{i+1}段: {seg['start_time']:.1f}s-{seg['end_time']:.1f}s, "
              f"质量={seg['quality']:.3f} ({quality_level})")

def compare_with_existing_methods():
    """与现有方法对比"""
    print("\n=== 与现有方法对比 ===")
    print()
    
    scorer = UnifiedTrajectoryScorer()
    
    # 测试案例：前好后差的长轨迹
    trajectory = ([0.5 + np.random.normal(0, 0.1) for _ in range(100)] + 
                  [3.0 + np.random.normal(0, 0.5) for _ in range(200)])
    trajectory = [max(0.1, d) for d in trajectory]
    duration = 30.0
    
    # 统一评分
    unified_score, unified_result = scorer.unified_scoring(trajectory, duration)
    
    # 传统评分
    scores = [scorer.distance_to_score(d) for d in trajectory]
    avg_score = np.mean(scores)
    duration_ratio = min(duration / 30, 1.0)
    stability_score = sum(1 for s in scores if s > 0.6) / len(scores)
    traditional_score = avg_score * 0.6 + duration_ratio * 0.3 + stability_score * 0.1
    
    # F1-style评分（简化版）
    spatial_precision = avg_score
    temporal_recall = math.sqrt(min(duration / 20, 1.0))
    f1_score = 2 * (spatial_precision * temporal_recall) / (spatial_precision + temporal_recall)
    direction_consistency = 0.9  # 假设方向一致性良好
    f1_final_score = f1_score * 0.8 + direction_consistency * 0.2
    
    print("前好后差长轨迹(30秒)对比:")
    print(f"传统评分:     {traditional_score:.3f} (被后段拖累)")
    print(f"F1-style评分: {f1_final_score:.3f} (被后段拖累)")
    print(f"统一评分:     {unified_score:.3f} (峰值优先策略)")
    print()
    
    print("优势分析:")
    print("- 统一评分识别出前10秒的高质量段，避免被后段拖累")
    print("- 传统方法和F1方法都被整体平均质量拖累")
    print("- 统一评分能够兼顾长轨迹的局部优势和短轨迹的整体质量")

if __name__ == "__main__":
    test_unified_scoring()
    compare_with_existing_methods()
