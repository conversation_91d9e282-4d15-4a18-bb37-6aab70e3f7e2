# RTK–感知候选轨迹提取方案

> 版本 v1.0  ·  最后更新：2025-07-25

## 1  背景与目标

在道路交叉口（含 L 形、S 形、掉头等复杂路径）场景下，**RTK 轨迹**往往呈折线或曲线形。直接用轴对齐矩形 (AABB) 做空间过滤会引入大量空白区域。为了在保持**高召回率**的同时**显著削减感知数据规模**，本文提出一种基于“**多段线缓冲多边形（Corridor Polygon）**”的候选轨迹提取方法。

目标：

* 仅保留 **RTK 行驶时间段**内、且 **整体位于 RTK 轨迹走廊** (25 m * 5 m) 的感知轨迹片段；
* 输出候选轨迹数量控制在原数据的 ≲ 1 %；
* 算法可扩展至百万 RTK 点、亿级感知点，内存 < 4 GB。

---

## 2  核心思路

1. **时间粗过滤**：按 RTK 起止时刻 ± 3 s 裁剪感知点；
2. **感知按 ID** 聚合为轨迹片段；
3. **RTK 每 1 s 采样**，形成等时间间隔点列；
4. **多段线缓冲**：对 RTK 采样点创建 25 m（纵向）× 5 m（横向）走廊多边形；
5. **空间索引**：构建 `STRtree`，快速判断感知轨迹是否落在走廊内；
6. **输出候选**：仅保留满足包含关系的感知轨迹与对应点。

---

## 3  详细流程

### 3.1  数据准备

| 数据 | 字段要求 | 备注 |
|------|----------|------|
| RTK | `timestamp, lat, lon` |  
| 感知 | `timestamp, id, lat, lon` | 同一 `id` 表示同一路侧目标 |

*所有时间字段需转换为 UTC 秒级整数，以便快速比较。*

### 3.2  RTK 轨迹按距离降采样

```python
def downsample_by_distance(rtk_df, interval_meters=10.0):
    """按距离间隔降采样RTK轨迹"""
    # 转换为UTM坐标计算距离
    coords = []
    for _, row in rtk_df.iterrows():
        x, y, zone = geo_utils.wgs84_to_utm(row['lat'], row['lon'])
        coords.append([x, y])
    coords = np.array(coords)
    
    # 计算累积距离
    distances = np.zeros(len(coords))
    for i in range(1, len(coords)):
        distances[i] = distances[i-1] + np.linalg.norm(coords[i] - coords[i-1])
    
    # 按距离间隔采样
    sampled_indices = [0]  # 总是保留第一个点
    current_distance = 0
    
    for i in range(1, len(distances)):
        if distances[i] >= current_distance + interval_meters:
            sampled_indices.append(i)
            current_distance = distances[i]
    
    # 总是保留最后一个点
    if sampled_indices[-1] != len(rtk_df) - 1:
        sampled_indices.append(len(rtk_df) - 1)
    
    return rtk_df.iloc[sampled_indices].reset_index(drop=True)
```

* **推荐间隔**：城市道路 10m，高速公路 20-30m，复杂路口 5-8m；
* **优势**：不受车速影响，几何特征保留更好，弯道细节完整；
* **效果**：1341 点 → 40 点（减少 97%），处理速度提升 5-10 倍。

### 3.3  生成走廊多边形（快速实现）

采用"首尾延长 + 一次 buffer"方法，完全取代逐段矩形拼接：

```python
import numpy as np
from shapely.geometry import LineString

def generate_corridor_fast(coords, long_buffer=25.0, lat_buffer=5.0):
    """快速生成走廊多边形"""
    # 1) 计算首尾方向向量并各延长
    u_head = (coords[1] - coords[0])
    u_head = u_head / np.linalg.norm(u_head)
    
    u_tail = (coords[-1] - coords[-2])
    u_tail = u_tail / np.linalg.norm(u_tail)
    
    head_pt = coords[0] - long_buffer * u_head
    tail_pt = coords[-1] + long_buffer * u_tail
    
    # 2) 拼接延长点后一次 buffer
    ext_coords = np.vstack([head_pt, coords, tail_pt])
    corridor_poly = LineString(ext_coords).buffer(
        lat_buffer,
        cap_style=2,   # Flat，两端平口正好停在延长点处
        join_style=2   # Miter，拐弯直角拼接
    ).buffer(0)        # 清理几何

    prepared_corridor = shapely.prepared.prep(corridor_poly)
    return corridor_poly, prepared_corridor
```

**性能对比**：
* 传统逐矩形方法：0.021s（135 点）→ 0.005s（40 点）
* **快速 buffer 方法**：0.010s（135 点）→ **0.001s（40 点）**
* **速度提升**：5 倍，且几何重叠率 >95%

**核心优势**：
* 完全在 GEOS C++ 层执行，无 Python 循环开销
* 自动处理拐弯连接，无几何缝隙
* 内存占用更少，边界点数减少 60-70%

### 3.4  感知轨迹片段化

```python
per_df = per_df.sort_values(['id', 'timestamp'])
tracks = {
    tid: grp for tid, grp in per_df.groupby('id')
    if len(grp) >= MIN_PTS and grp['timestamp'].ptp() >= MIN_DUR
}
```

* 推荐 `MIN_PTS = 3`, `MIN_DUR = 0.5 s`；
* 转换每条 `grp[['x','y']]` 为 `LineString` 或 `MultiPoint`。

### 3.5  空间索引与包含判定

```python
from shapely.strtree import STRtree
geoms = [g.geom for g in tracks.values()]
idx_to_tid = {i: tid for i, tid in enumerate(tracks)}
str_tree = STRtree(geoms)

# 粗筛：bbox 相交
cand_idx = str_tree.query(prepared_corridor.bounds)

# 精筛：within / intersects
final_ids = [idx_to_tid[i] for i in cand_idx
             if prepared_corridor.contains(geoms[i])]
```

### 3.6  候选集合并与输出

```python
candidates = per_df[per_df['id'].isin(final_ids)].copy()
```

* 可加入去重：若同一 `id` 对应多个片段，只保留与走廊重叠最长者。

---

## 4  关键参数

| 参数 | 默认值 | 作用 |
|------|--------|------|
| `dt_buffer` | 3 s | 时间窗扩展 (RTK 起止 ± dt) |
| `interval_meters` | 10 m | 按距离降采样间隔 |
| `long_buffer` | 25 m | 沿轨迹方向缓冲半长 |
| `lat_buffer` | 5 m | 垂直轨迹方向缓冲半宽 |
| `min_pts` | 3 | 感知轨迹最少点数 |
| `min_dur` | 0.5 s | 感知轨迹最短持续时间 |

---

## 5  性能与效果评估

| 数据规模 | 步骤 | 耗时 (单核) | 备注 |
|-----------|------|-------------|------|
| 1341 RTK 点<br>3000 万 感知点 | 距离降采样 | < 0.01 s | 1341 → 40 点 |
| 同上 | 快速多边形构建 | < 0.001 s | 一次 buffer 完成 |
| 同上 | STRtree 查询 | ≈ 5 s | 含粗筛 + 精筛 |
| **输出** | 轨迹 ≤ 3 k 条 | 压缩率 > 99.9 % |

*快速实现相比传统方法速度提升 5-10 倍。*

---

## 6  优缺点对比

| 方案 | 优点 | 缺点 |
|------|------|------|
| KDTree 点级过滤 | 无须 Shapely；支持逐点横向距离精算 | 结果为点级，需再聚合；轨迹级特性缺失 |
| **本方案（快速实现）** | 轨迹层级过滤，极高性能；几何准确 | 依赖 Shapely 2.0+；需要 GEOS 支持 |

---

## 7  结论

通过 **RTK 按距离降采样 + 快速一次 buffer 多边形 + STRtree**，可在复杂道路场景下快速剔除 99 %以上无关感知轨迹，为后续匹配与诊断显著减负。新实现相比传统方法：

* **数据量减少 97%**：1341 点 → 40 点
* **处理速度提升 5 倍**：0.005s → 0.001s  
* **几何精度保持**：重叠率 >95%
* **内存占用更少**：边界点减少 60-70%

默认参数已足够覆盖十字左/右转与 S 形弯道；如需更强召回，适当放宽 `long_buffer`/`lat_buffer` 即可。