#!/usr/bin/env python3
"""
走廊过滤功能测试脚本
验证RTK走廊过滤器的功能和性能
"""

import os
import sys
import time
import json
from pathlib import Path

# 添加core模块到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'core'))

from core.config_loader import load_config
from core.data_utils import DataLoader
from core.simple_distance_matcher import SimpleDistanceMatcher

def test_corridor_filter():
    """测试走廊过滤功能"""
    print("🧪 走廊过滤功能测试")
    print("=" * 60)
    
    # 1. 加载配置
    print("1. 加载配置...")
    config = load_config('config/unified_config.json')
    print(f"   走廊过滤启用: {getattr(config, 'corridor_enabled', 'N/A')}")
    print(f"   降采样间隔: {getattr(config, 'corridor_downsample_interval_meters', 'N/A')}m")
    print(f"   纵向缓冲: {getattr(config, 'corridor_long_buffer_meters', 'N/A')}m")
    print(f"   横向缓冲: {getattr(config, 'corridor_lat_buffer_meters', 'N/A')}m")
    
    # 调试：打印所有配置属性
    print("   配置属性调试:")
    for attr in dir(config):
        if not attr.startswith('_') and 'corridor' in attr:
            print(f"     {attr} = {getattr(config, attr)}")
    
    # 2. 加载数据
    print("\n2. 加载测试数据...")
    rtk_file = "./data/rtk_part005.txt"
    perception_file = "./data/AJ06993PAJ00115B1.txt"
    
    if not os.path.exists(rtk_file) or not os.path.exists(perception_file):
        print("❌ 测试数据文件不存在")
        return False
    
    # 首先预处理原始数据
    from core.preprocessor import RawDataPreprocessor
    preprocessor = RawDataPreprocessor()
    
    print("   预处理RTK数据...")
    rtk_csv_path = preprocessor.preprocess_rtk_file(rtk_file)
    
    print("   预处理感知数据...")
    perception_csv_path = preprocessor.preprocess_perception_file(perception_file)
    
    # 然后加载预处理后的CSV数据
    data_loader = DataLoader(config)
    rtk_points = data_loader.load_rtk_csv(rtk_csv_path)
    perception_points = data_loader.load_perception_csv(perception_csv_path)
    
    print(f"   RTK点数: {len(rtk_points)}")
    print(f"   感知点数: {len(perception_points)}")
    
    # 时间同步
    rtk_points = data_loader.sync_rtk_time(rtk_points)
    perception_points = data_loader.sync_perception_time(perception_points)
    
    # 3. 创建匹配器
    print("\n3. 创建匹配器...")
    matcher = SimpleDistanceMatcher(config, rtk_points)
    
    # 检查走廊过滤器是否可用
    corridor_available = matcher.corridor_filter is not None
    print(f"   走廊过滤器可用: {corridor_available}")
    
    if corridor_available:
        stats = matcher.corridor_filter.get_corridor_stats()
        print(f"   Shapely可用: {stats['shapely_available']}")
        print(f"   走廊过滤启用: {stats['enabled']}")
    
    # 4. 测试传统ROI过滤
    print("\n4. 测试传统ROI过滤...")
    start_time = time.time()
    roi_filtered = matcher.roi_filter(perception_points)
    roi_time = time.time() - start_time
    
    print(f"   过滤前: {len(perception_points)} 点")
    print(f"   过滤后: {len(roi_filtered)} 点")
    print(f"   过滤率: {(1 - len(roi_filtered)/len(perception_points))*100:.1f}%")
    print(f"   耗时: {roi_time:.3f}秒")
    
    # 5. 测试走廊过滤（如果可用）
    corridor_filtered = []
    corridor_time = 0
    
    if corridor_available:
        print("\n5. 测试走廊过滤...")
        try:
            start_time = time.time()
            corridor_filtered = matcher.filter_perception_points(perception_points, use_corridor=True)
            corridor_time = time.time() - start_time
            
            print(f"   过滤前: {len(perception_points)} 点")
            print(f"   过滤后: {len(corridor_filtered)} 点")
            print(f"   过滤率: {(1 - len(corridor_filtered)/len(perception_points))*100:.1f}%")
            print(f"   耗时: {corridor_time:.3f}秒")
            
        except Exception as e:
            print(f"   ❌ 走廊过滤失败: {e}")
            import traceback
            traceback.print_exc()
    else:
        print("\n5. 走廊过滤不可用，跳过测试")
    
    # 6. 性能对比
    print("\n6. 性能对比...")
    if corridor_available and len(corridor_filtered) > 0:
        speed_improvement = roi_time / corridor_time if corridor_time > 0 else float('inf')
        print(f"   速度对比: 走廊过滤 vs ROI过滤")
        print(f"   走廊过滤耗时: {corridor_time:.3f}秒")
        print(f"   ROI过滤耗时: {roi_time:.3f}秒")
        print(f"   速度差异: {speed_improvement:.1f}x")
        
        # 数据量对比
        roi_reduction = (1 - len(roi_filtered)/len(perception_points))*100
        corridor_reduction = (1 - len(corridor_filtered)/len(perception_points))*100
        print(f"   数据减少率对比:")
        print(f"   ROI过滤: {roi_reduction:.1f}%")
        print(f"   走廊过滤: {corridor_reduction:.1f}%")
        
        # 结果集重叠分析
        roi_ids = set(p.id for p in roi_filtered)
        corridor_ids = set(p.id for p in corridor_filtered)
        overlap = len(roi_ids & corridor_ids)
        roi_only = len(roi_ids - corridor_ids)
        corridor_only = len(corridor_ids - roi_ids)
        
        print(f"   结果集对比:")
        print(f"   重叠ID数: {overlap}")
        print(f"   仅ROI过滤: {roi_only}")
        print(f"   仅走廊过滤: {corridor_only}")
        
    else:
        print("   无法进行性能对比")
    
    # 7. 测试结果保存
    print("\n7. 保存测试结果...")
    results = {
        'test_time': time.strftime('%Y-%m-%d %H:%M:%S'),
        'config': {
            'corridor_enabled': getattr(config, 'corridor_enabled', False),
            'corridor_available': corridor_available,
            'shapely_available': stats.get('shapely_available', False) if corridor_available else False
        },
        'data': {
            'rtk_points': len(rtk_points),
            'perception_points': len(perception_points)
        },
        'roi_filter': {
            'filtered_points': len(roi_filtered),
            'reduction_rate': (1 - len(roi_filtered)/len(perception_points))*100,
            'time_seconds': roi_time
        }
    }
    
    if corridor_available and len(corridor_filtered) > 0:
        results['corridor_filter'] = {
            'filtered_points': len(corridor_filtered),
            'reduction_rate': (1 - len(corridor_filtered)/len(perception_points))*100,
            'time_seconds': corridor_time,
            'speed_vs_roi': roi_time / corridor_time if corridor_time > 0 else float('inf')
        }
    
    # 保存结果
    os.makedirs('output', exist_ok=True)
    result_file = f"output/corridor_filter_test_{int(time.time())}.json"
    with open(result_file, 'w', encoding='utf-8') as f:
        json.dump(results, f, indent=2, ensure_ascii=False)
    
    print(f"   测试结果已保存到: {result_file}")
    
    # 8. 总结
    print("\n" + "=" * 60)
    print("🎯 测试总结")
    print("=" * 60)
    
    if corridor_available:
        print("✅ 走廊过滤功能已集成并可用")
        if len(corridor_filtered) > 0:
            print("✅ 走廊过滤测试成功")
            print(f"✅ 数据减少率: {(1 - len(corridor_filtered)/len(perception_points))*100:.1f}%")
        else:
            print("⚠️ 走廊过滤未产生结果，可能需要调整参数")
    else:
        print("❌ 走廊过滤功能不可用")
        print("💡 可能原因: Shapely未安装或配置问题")
    
    print("✅ 传统ROI过滤功能正常")
    print(f"✅ ROI过滤数据减少率: {(1 - len(roi_filtered)/len(perception_points))*100:.1f}%")
    
    return True

if __name__ == "__main__":
    try:
        success = test_corridor_filter()
        if success:
            print("\n🎉 测试完成！")
        else:
            print("\n❌ 测试失败！")
    except Exception as e:
        print(f"\n💥 测试异常: {e}")
        import traceback
        traceback.print_exc() 