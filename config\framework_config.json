{"roi": {"roi_long": 20.0, "roi_lat": 5.0}, "dtw": {"win_sec": 3.0, "local_match_thr": 0.8, "split_match_thr": 0.7, "dtw_radius": 5}, "matching": {"overlap_min": 0.5, "max_gap": 2.0, "gap_match_thr": 0.5, "max_missing_gap": 5.0, "min_missing_gap": 0.5, "rtk_buffer": 5.0, "good_match_thr": 0.6, "min_segment_length": 2}, "scoring": {"peak_weight": 0.6, "duration_weight": 0.3, "stability_weight": 0.1}, "anomaly": {"switch_dt": 2.0, "switch_dist": 10.0, "switch_speed": 5.0, "switch_heading": 30.0}, "processing": {"time_sync_enabled": true, "spatial_filter_enabled": true, "data_validation_enabled": true, "auto_format_detection": true}, "output": {"generate_csv": true, "generate_json": true, "generate_plots": false, "include_debug_info": true}}