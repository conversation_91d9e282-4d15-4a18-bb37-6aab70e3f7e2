#!/usr/bin/env python3
"""
感知轨迹与RTK走廊匹配分析脚本
分析有多少感知轨迹线段落在RTK走廊内
"""

import sys
import os
import numpy as np
import pandas as pd
from datetime import datetime
import matplotlib.pyplot as plt
from collections import defaultdict

# 设置matplotlib中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

# 添加core模块到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'core'))

try:
    from shapely.geometry import LineString, Point
    from shapely.ops import unary_union
    import shapely.prepared
    SHAPELY_AVAILABLE = True
except ImportError:
    print("错误: 需要安装shapely库")
    sys.exit(1)

from core.data_processing.readers.nmea_reader import NMEAReader
from core.utils.geo_utils import GeoUtils


class PerceptionTrajectoryMatcher:
    """感知轨迹匹配器"""
    
    def __init__(self):
        self.geo_utils = GeoUtils()
    
    def parse_rtk_data(self, rtk_file: str) -> pd.DataFrame:
        """解析RTK数据"""
        print(f"正在解析RTK数据: {rtk_file}")
        reader = NMEAReader(rtk_file)
        raw_records = reader.read()
        
        rmc_records = [r for r in raw_records if r and r.get('type') == 'RMC']
        if not rmc_records:
            raise ValueError("未找到有效的RMC记录")
        
        df = pd.DataFrame(rmc_records)
        df['timestamp'] = pd.to_datetime(df['timestamp'])
        
        # 统一转换为北京时间（UTC+8）
        if df['timestamp'].dt.tz is None:
            # 如果是naive datetime，假设为UTC时间，然后转换为北京时间
            df['timestamp'] = df['timestamp'].dt.tz_localize('UTC').dt.tz_convert('Asia/Shanghai')
        else:
            # 如果已有时区信息，直接转换为北京时间
            df['timestamp'] = df['timestamp'].dt.tz_convert('Asia/Shanghai')
        
        df = df.sort_values('timestamp').reset_index(drop=True)
        df = df.dropna(subset=['lat', 'lon'])
        
        print(f"RTK数据: {len(df)} 条记录")
        return df
    
    def parse_perception_data(self, perception_file: str) -> pd.DataFrame:
        """解析感知数据 - JSON格式"""
        print(f"正在解析感知数据: {perception_file}")
        
        import json
        data = []
        
        with open(perception_file, 'r', encoding='utf-8') as f:
            for line_num, line in enumerate(f, 1):
                line = line.strip()
                if not line:
                    continue
                
                try:
                    # 解析JSON数据
                    json_data = json.loads(line)
                    
                    # 提取时间戳
                    timestamp_str = json_data.get('Timestamp', '')
                    if not timestamp_str:
                        continue
                    
                    # 转换时间戳为北京时间
                    timestamp = pd.to_datetime(timestamp_str)
                    if timestamp.tz is None:
                        # 如果是naive datetime，假设为北京时间
                        timestamp = timestamp.tz_localize('Asia/Shanghai')
                    else:
                        # 如果已有时区信息，转换为北京时间
                        timestamp = timestamp.tz_convert('Asia/Shanghai')
                    
                    # 提取目标列表
                    obj_list = json_data.get('Obj_List', [])
                    
                    for obj in obj_list:
                        obj_id = obj.get('ID')
                        lat = obj.get('PtcLat')
                        lon = obj.get('PtcLon')
                        
                        if obj_id and lat is not None and lon is not None:
                            data.append({
                                'timestamp': timestamp,
                                'id': obj_id,
                                'lat': lat,
                                'lon': lon,
                                'heading': obj.get('PtcHeading', 0.0),
                                'speed': obj.get('PtcSpeed', 0.0),
                                'vehicle_class': obj.get('vehicleClass', 0),
                                'ptc_type': obj.get('PtcType', 0)
                            })
                
                except (json.JSONDecodeError, ValueError, KeyError) as e:
                    if line_num <= 5:  # 只显示前5行的解析错误
                        print(f"跳过第{line_num}行: JSON解析错误: {e}")
                    continue
        
        if not data:
            raise ValueError("未找到有效的感知数据")
        
        df = pd.DataFrame(data)
        df = df.sort_values(['id', 'timestamp']).reset_index(drop=True)
        
        print(f"感知数据: {len(df)} 条记录, {df['id'].nunique()} 个不同目标")
        print(f"时间范围: {df['timestamp'].min()} - {df['timestamp'].max()}")
        return df
    
    def downsample_rtk_by_distance(self, rtk_df: pd.DataFrame, interval_meters=10.0) -> tuple:
        """按距离降采样RTK数据"""
        print(f"正在按距离降采样RTK数据，间隔: {interval_meters}米")
        
        # 转换为UTM坐标
        coords = []
        for _, row in rtk_df.iterrows():
            x, y, zone = self.geo_utils.wgs84_to_utm(row['lat'], row['lon'])
            coords.append([x, y])
        coords = np.array(coords)
        
        # 计算累积距离
        distances = np.zeros(len(coords))
        for i in range(1, len(coords)):
            distances[i] = distances[i-1] + np.linalg.norm(coords[i] - coords[i-1])
        
        # 按距离间隔采样
        sampled_indices = [0]
        current_distance = 0
        
        for i in range(1, len(distances)):
            if distances[i] >= current_distance + interval_meters:
                sampled_indices.append(i)
                current_distance = distances[i]
        
        if sampled_indices[-1] != len(rtk_df) - 1:
            sampled_indices.append(len(rtk_df) - 1)
        
        downsampled_df = rtk_df.iloc[sampled_indices].reset_index(drop=True)
        downsampled_coords = coords[sampled_indices]
        
        print(f"降采样后剩余 {len(downsampled_df)} 个点")
        return downsampled_df, downsampled_coords
    
    def generate_corridor_fast(self, coords: np.ndarray, long_buffer=25.0, lat_buffer=5.0):
        """快速生成走廊多边形"""
        print(f"正在生成走廊多边形，纵向±{long_buffer}m，横向±{lat_buffer}m")
        
        if len(coords) < 2:
            raise ValueError("坐标点数量不足")
        
        # 计算首尾方向向量并各延长
        u_head = (coords[1] - coords[0])
        u_head = u_head / np.linalg.norm(u_head)
        
        u_tail = (coords[-1] - coords[-2])
        u_tail = u_tail / np.linalg.norm(u_tail)
        
        head_pt = coords[0] - long_buffer * u_head
        tail_pt = coords[-1] + long_buffer * u_tail
        
        # 拼接延长点后一次buffer
        ext_coords = np.vstack([head_pt, coords, tail_pt])
        corridor_poly = LineString(ext_coords).buffer(
            lat_buffer,
            cap_style=2,   # Flat
            join_style=2   # Miter
        ).buffer(0)
        
        prepared_corridor = shapely.prepared.prep(corridor_poly)
        
        print(f"走廊多边形生成完成，面积: {corridor_poly.area:.1f}m²")
        return corridor_poly, prepared_corridor
    
    def filter_perception_by_time(self, perception_df: pd.DataFrame, rtk_df: pd.DataFrame, 
                                time_buffer=3600.0) -> pd.DataFrame:
        """按时间窗过滤感知数据 - 统一北京时间"""
        # 确保两个数据集的时间戳都在同一时区（北京时间）
        rtk_timestamps = pd.to_datetime(rtk_df['timestamp'])
        perception_timestamps = pd.to_datetime(perception_df['timestamp'])
        
        # 如果有时区信息，都转换为北京时间
        if rtk_timestamps.dt.tz is not None:
            rtk_timestamps = rtk_timestamps.dt.tz_convert('Asia/Shanghai')
        else:
            rtk_timestamps = rtk_timestamps.dt.tz_localize('Asia/Shanghai')
            
        if perception_timestamps.dt.tz is not None:
            perception_timestamps = perception_timestamps.dt.tz_convert('Asia/Shanghai')
        else:
            perception_timestamps = perception_timestamps.dt.tz_localize('Asia/Shanghai')
        
        print(f"RTK时间范围: {rtk_timestamps.min()} - {rtk_timestamps.max()}")
        print(f"感知时间范围: {perception_timestamps.min()} - {perception_timestamps.max()}")
        
        # 检查时间是否重叠
        time_gap = abs((rtk_timestamps.min() - perception_timestamps.min()).total_seconds())
        print(f"时间差: {time_gap:.0f}秒 ({time_gap/3600:.1f}小时)")
        
        # 只有在时间差超过24小时时才跳过时间过滤
        if time_gap > 86400:  # 超过24小时
            print("⚠️ 警告: RTK和感知数据时间差超过24小时，可能不是同一时段数据")
            print("🔄 跳过时间过滤，使用所有感知数据进行空间匹配")
            return perception_df.copy()
        
        # 正常时间过滤 - 使用较小的时间缓冲区
        # 如果时间差很小，使用更精确的时间过滤
        if time_gap < 3600:  # 小于1小时，使用较小的缓冲区
            actual_time_buffer = min(time_buffer, 300.0)  # 最多5分钟缓冲
            print(f"🕐 时间差较小，使用精确时间过滤，缓冲区: {actual_time_buffer:.0f}秒")
        else:
            actual_time_buffer = time_buffer
            print(f"🕐 使用标准时间过滤，缓冲区: {actual_time_buffer:.0f}秒")
        
        rtk_start = rtk_timestamps.min() - pd.Timedelta(seconds=actual_time_buffer)
        rtk_end = rtk_timestamps.max() + pd.Timedelta(seconds=actual_time_buffer)
        
        filtered = perception_df[
            (perception_timestamps >= rtk_start) & 
            (perception_timestamps <= rtk_end)
        ].copy()
        
        print(f"过滤时间窗: {rtk_start} - {rtk_end}")
        print(f"时间过滤后感知数据: {len(filtered)} 条记录, {filtered['id'].nunique()} 个目标")
        
        # 如果过滤后数据太少，给出提示
        if len(filtered) == 0:
            print("⚠️ 时间过滤后没有数据，可能需要调整时间缓冲区")
        elif len(filtered) < len(perception_df) * 0.1:  # 少于10%
            print(f"⚠️ 时间过滤后数据量较少 ({len(filtered)}/{len(perception_df)}), 可能需要调整时间缓冲区")
        
        return filtered
    
    def create_perception_trajectories(self, perception_df: pd.DataFrame, 
                                     min_points=3, min_duration=0.5) -> dict:
        """创建感知轨迹线段"""
        print("正在创建感知轨迹线段...")
        
        trajectories = {}
        
        for obj_id, group in perception_df.groupby('id'):
            group = group.sort_values('timestamp').reset_index(drop=True)
            
            # 过滤条件
            if len(group) < min_points:
                continue
            
            duration = (group['timestamp'].iloc[-1] - group['timestamp'].iloc[0]).total_seconds()
            if duration < min_duration:
                continue
            
            # 转换为UTM坐标
            coords = []
            for _, row in group.iterrows():
                x, y, zone = self.geo_utils.wgs84_to_utm(row['lat'], row['lon'])
                coords.append([x, y])
            coords = np.array(coords)
            
            # 创建LineString
            if len(coords) >= 2:
                trajectory = LineString(coords)
                trajectories[obj_id] = {
                    'geometry': trajectory,
                    'coords': coords,
                    'data': group,
                    'length': trajectory.length,
                    'duration': duration,
                    'points': len(coords)
                }
        
        print(f"创建了 {len(trajectories)} 条有效轨迹线段")
        return trajectories
    
    def calculate_trajectory_direction(self, coords: np.ndarray) -> np.ndarray:
        """计算轨迹的整体方向向量"""
        if len(coords) < 2:
            return np.array([0.0, 0.0])
        
        # 使用起点到终点的向量作为整体方向
        direction = coords[-1] - coords[0]
        
        # 归一化方向向量
        norm = np.linalg.norm(direction)
        if norm > 0:
            return direction / norm
        else:
            return np.array([0.0, 0.0])
    
    def check_direction_consistency(self, traj_coords: np.ndarray, rtk_coords: np.ndarray, 
                                  threshold_angle=60.0) -> bool:
        """检查轨迹方向是否与RTK轨迹方向一致
        
        Args:
            traj_coords: 感知轨迹坐标
            rtk_coords: RTK轨迹坐标
            threshold_angle: 角度阈值（度），小于此角度认为方向一致
            
        Returns:
            bool: True表示方向一致，False表示方向不一致
        """
        # 计算两条轨迹的方向向量
        traj_direction = self.calculate_trajectory_direction(traj_coords)
        rtk_direction = self.calculate_trajectory_direction(rtk_coords)
        
        # 如果任一方向向量为零向量，返回False
        if np.linalg.norm(traj_direction) == 0 or np.linalg.norm(rtk_direction) == 0:
            return False
        
        # 计算两个方向向量的夹角
        dot_product = np.dot(traj_direction, rtk_direction)
        # 限制dot_product在[-1, 1]范围内，避免数值误差
        dot_product = np.clip(dot_product, -1.0, 1.0)
        
        angle_rad = np.arccos(dot_product)
        angle_deg = np.degrees(angle_rad)
        
        # 检查角度是否小于阈值
        return angle_deg <= threshold_angle

    def match_trajectories_to_corridor(self, trajectories: dict, prepared_corridor, 
                                     rtk_coords: np.ndarray, rtk_df: pd.DataFrame) -> dict:
        """匹配轨迹到走廊 - 完全在走廊内 + 方向一致 + 时间重合"""
        print("正在匹配轨迹到走廊...")
        
        results = {
            'matched': {},
            'unmatched': {},
            'stats': {
                'total_trajectories': len(trajectories),
                'matched_count': 0,
                'unmatched_count': 0,
                'matched_points': 0,
                'total_points': 0,
                'matched_length': 0.0,
                'total_length': 0.0,
                'failed_spatial': 0,
                'failed_direction': 0,
                'failed_time': 0
            }
        }
        
        if len(trajectories) == 0:
            print("⚠️ 没有轨迹数据进行匹配")
            results['stats'].update({
                'match_rate_by_count': 0.0,
                'match_rate_by_points': 0.0,
                'match_rate_by_length': 0.0
            })
            return results
        
        # 计算RTK轨迹的时间范围
        rtk_start_time = rtk_df['timestamp'].min()
        rtk_end_time = rtk_df['timestamp'].max()
        
        print(f"RTK时间范围: {rtk_start_time} - {rtk_end_time}")
        print(f"开始匹配检查: 空间包含 + 方向一致 + 时间重合")
        
        for obj_id, traj_info in trajectories.items():
            geometry = traj_info['geometry']
            coords = traj_info['coords']
            traj_data = traj_info['data']
            
            # 条件1: 轨迹完全在走廊内
            if not prepared_corridor.contains(geometry):
                results['unmatched'][obj_id] = traj_info
                results['stats']['unmatched_count'] += 1
                results['stats']['failed_spatial'] += 1
                results['stats']['total_points'] += traj_info['points']
                results['stats']['total_length'] += traj_info['length']
                continue
            
            # 条件2: 行走方向与RTK轨迹一致
            if not self.check_direction_consistency(coords, rtk_coords, threshold_angle=60.0):
                results['unmatched'][obj_id] = traj_info
                results['stats']['unmatched_count'] += 1
                results['stats']['failed_direction'] += 1
                results['stats']['total_points'] += traj_info['points']
                results['stats']['total_length'] += traj_info['length']
                continue
            
            # 条件3: 时间段重合检查
            traj_start_time = traj_data['timestamp'].min()
            traj_end_time = traj_data['timestamp'].max()
            
            # 检查时间段是否有重合（允许一定的时间缓冲）
            time_buffer = pd.Timedelta(seconds=300)  # 5分钟缓冲
            rtk_start_buffered = rtk_start_time - time_buffer
            rtk_end_buffered = rtk_end_time + time_buffer
            
            # 时间段重合判断：两个时间段有交集
            time_overlap = not (traj_end_time < rtk_start_buffered or traj_start_time > rtk_end_buffered)
            
            if not time_overlap:
                results['unmatched'][obj_id] = traj_info
                results['stats']['unmatched_count'] += 1
                results['stats']['failed_time'] += 1
                results['stats']['total_points'] += traj_info['points']
                results['stats']['total_length'] += traj_info['length']
                continue
            
            # 所有条件都满足，轨迹匹配成功
            results['matched'][obj_id] = traj_info
            results['stats']['matched_count'] += 1
            results['stats']['matched_points'] += traj_info['points']
            results['stats']['matched_length'] += traj_info['length']
            results['stats']['total_points'] += traj_info['points']
            results['stats']['total_length'] += traj_info['length']
        
        # 计算匹配率 - 防止除零
        stats = results['stats']
        stats['match_rate_by_count'] = (stats['matched_count'] / stats['total_trajectories'] * 100) if stats['total_trajectories'] > 0 else 0.0
        stats['match_rate_by_points'] = (stats['matched_points'] / stats['total_points'] * 100) if stats['total_points'] > 0 else 0.0
        stats['match_rate_by_length'] = (stats['matched_length'] / stats['total_length'] * 100) if stats['total_length'] > 0 else 0.0
        
        return results
    
    def print_matching_results(self, results: dict):
        """打印匹配结果"""
        stats = results['stats']
        
        print(f"\n{'='*60}")
        print("感知轨迹匹配结果统计")
        print(f"{'='*60}")
        
        print(f"📊 总体统计:")
        print(f"  总轨迹数量: {stats['total_trajectories']}")
        print(f"  总数据点数: {stats['total_points']}")
        print(f"  总轨迹长度: {stats['total_length']:.1f}m")
        
        print(f"\n✅ 匹配到走廊内:")
        print(f"  匹配轨迹数: {stats['matched_count']}")
        print(f"  匹配数据点: {stats['matched_points']}")
        print(f"  匹配轨迹长度: {stats['matched_length']:.1f}m")
        
        print(f"\n❌ 未匹配到走廊:")
        print(f"  未匹配轨迹数: {stats['unmatched_count']}")
        print(f"  未匹配数据点: {stats['total_points'] - stats['matched_points']}")
        print(f"  未匹配轨迹长度: {stats['total_length'] - stats['matched_length']:.1f}m")
        
        print(f"\n📈 匹配率:")
        print(f"  按轨迹数量: {stats['match_rate_by_count']:.1f}%")
        print(f"  按数据点数: {stats['match_rate_by_points']:.1f}%")
        print(f"  按轨迹长度: {stats['match_rate_by_length']:.1f}%")
        
        print(f"\n🎯 匹配轨迹详情:")
        for obj_id, traj_info in list(results['matched'].items())[:10]:  # 显示前10个
            print(f"  ID {obj_id}: {traj_info['points']}点, {traj_info['length']:.1f}m, {traj_info['duration']:.1f}s")
        
        if len(results['matched']) > 10:
            print(f"  ... 还有 {len(results['matched']) - 10} 条匹配轨迹")
        
        print(f"{'='*60}")
    
    def visualize_matching(self, rtk_coords: np.ndarray, corridor_poly, 
                          results: dict, save_path: str = None):
        """可视化匹配结果"""
        print("正在生成匹配结果可视化...")
        
        fig, ax = plt.subplots(1, 1, figsize=(12, 10))
        
        # 绘制走廊多边形
        if hasattr(corridor_poly, 'exterior'):
            x, y = corridor_poly.exterior.xy
            ax.fill(x, y, alpha=0.3, color='lightblue', label='RTK走廊')
            ax.plot(x, y, 'b-', linewidth=1)
        
        # 绘制RTK轨迹
        ax.plot(rtk_coords[:, 0], rtk_coords[:, 1], 'r-', linewidth=3, label='RTK轨迹')
        ax.scatter(rtk_coords[0, 0], rtk_coords[0, 1], c='green', s=100, marker='o', label='起点', zorder=6)
        ax.scatter(rtk_coords[-1, 0], rtk_coords[-1, 1], c='orange', s=100, marker='s', label='终点', zorder=6)
        
        # 绘制匹配的感知轨迹
        matched_count = 0
        for obj_id, traj_info in results['matched'].items():
            coords = traj_info['coords']
            ax.plot(coords[:, 0], coords[:, 1], 'g-', linewidth=1, alpha=0.7)
            matched_count += 1
            if matched_count == 1:  # 只在第一条轨迹上添加标签
                ax.plot([], [], 'g-', linewidth=1, alpha=0.7, label=f'匹配轨迹({len(results["matched"])}条)')
        
        # 绘制未匹配的感知轨迹
        unmatched_count = 0
        for obj_id, traj_info in results['unmatched'].items():
            coords = traj_info['coords']
            ax.plot(coords[:, 0], coords[:, 1], 'gray', linewidth=1, alpha=0.5)
            unmatched_count += 1
            if unmatched_count == 1:  # 只在第一条轨迹上添加标签
                ax.plot([], [], 'gray', linewidth=1, alpha=0.5, label=f'未匹配轨迹({len(results["unmatched"])}条)')
        
        # 设置图形属性
        ax.set_xlabel('UTM X (米)', fontsize=12)
        ax.set_ylabel('UTM Y (米)', fontsize=12)
        ax.set_title(f'感知轨迹匹配结果\n'
                    f'匹配率: {results["stats"]["match_rate_by_count"]:.1f}% '
                    f'({results["stats"]["matched_count"]}/{results["stats"]["total_trajectories"]}条)', 
                    fontsize=14)
        ax.legend(fontsize=10)
        ax.grid(True, alpha=0.3)
        ax.set_aspect('equal')
        
        plt.tight_layout()
        
        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
            print(f"可视化结果已保存到: {save_path}")
        
        plt.show()

    def export_to_qgis(self, rtk_coords: np.ndarray, corridor_poly, results: dict, 
                      rtk_df: pd.DataFrame, output_dir: str = "output/qgis"):
        """导出为QGIS可直接查看的地理数据文件"""
        print("正在生成QGIS兼容的地理数据文件...")
        
        import json
        
        os.makedirs(output_dir, exist_ok=True)
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        
        try:
            # 1. RTK轨迹线 - 直接使用原始WGS84坐标
            print("生成RTK轨迹线...")
            rtk_wgs84_coords = []
            for _, row in rtk_df.iterrows():
                rtk_wgs84_coords.append([float(row['lon']), float(row['lat'])])  # GeoJSON格式为[lon, lat]
            
            rtk_geojson = {
                "type": "FeatureCollection",
                "features": [{
                    "type": "Feature",
                    "geometry": {
                        "type": "LineString",
                        "coordinates": rtk_wgs84_coords
                    },
                    "properties": {
                        "name": "RTK轨迹",
                        "type": "rtk_trajectory",
                        "points_count": len(rtk_wgs84_coords),
                        "start_time": str(rtk_df['timestamp'].min()),
                        "end_time": str(rtk_df['timestamp'].max())
                    }
                }]
            }
            
            rtk_file = os.path.join(output_dir, f"rtk_trajectory_{timestamp}.geojson")
            with open(rtk_file, 'w', encoding='utf-8') as f:
                json.dump(rtk_geojson, f, ensure_ascii=False, indent=2)
            print(f"RTK轨迹已保存到: {rtk_file}")
            
            # 2. 走廊多边形 - 使用近似方法转换回WGS84
            print("生成走廊多边形...")
            # 获取RTK轨迹的边界框来估算坐标范围
            rtk_lat_mean = rtk_df['lat'].mean()
            rtk_lon_mean = rtk_df['lon'].mean()
            
            # 简单的近似转换：基于中心点的偏移
            corridor_exterior = list(corridor_poly.exterior.coords)
            corridor_wgs84_coords = []
            
            # 计算UTM到WGS84的近似转换因子
            lat_factor = 1.0 / 111320.0  # 纬度1度约111320米
            lon_factor = 1.0 / (111320.0 * np.cos(np.radians(rtk_lat_mean)))  # 经度因子随纬度变化
            
            # 获取RTK轨迹中心点的UTM坐标作为参考
            center_utm_x, center_utm_y, _ = self.geo_utils.wgs84_to_utm(rtk_lat_mean, rtk_lon_mean)
            
            for coord in corridor_exterior:
                # 计算相对于中心点的偏移
                dx = coord[0] - center_utm_x
                dy = coord[1] - center_utm_y
                
                # 转换为WGS84偏移
                lat_offset = dy * lat_factor
                lon_offset = dx * lon_factor
                
                # 计算最终坐标
                final_lat = rtk_lat_mean + lat_offset
                final_lon = rtk_lon_mean + lon_offset
                
                corridor_wgs84_coords.append([final_lon, final_lat])
            
            corridor_geojson = {
                "type": "FeatureCollection",
                "features": [{
                    "type": "Feature",
                    "geometry": {
                        "type": "Polygon",
                        "coordinates": [corridor_wgs84_coords]
                    },
                    "properties": {
                        "name": "RTK走廊",
                        "type": "corridor",
                        "area_m2": float(corridor_poly.area),
                        "perimeter_m": float(corridor_poly.length)
                    }
                }]
            }
            
            corridor_file = os.path.join(output_dir, f"rtk_corridor_{timestamp}.geojson")
            with open(corridor_file, 'w', encoding='utf-8') as f:
                json.dump(corridor_geojson, f, ensure_ascii=False, indent=2)
            print(f"走廊多边形已保存到: {corridor_file}")
            
            # 3. 匹配的感知轨迹 - 直接使用原始坐标
            print("生成匹配的感知轨迹...")
            matched_features = []
            for obj_id, traj_info in results['matched'].items():
                # 直接使用原始感知数据的WGS84坐标
                traj_data = traj_info['data']
                traj_wgs84_coords = []
                for _, row in traj_data.iterrows():
                    traj_wgs84_coords.append([float(row['lon']), float(row['lat'])])
                
                matched_features.append({
                    "type": "Feature",
                    "geometry": {
                        "type": "LineString",
                        "coordinates": traj_wgs84_coords
                    },
                    "properties": {
                        "id": str(obj_id),
                        "name": f"感知轨迹_{obj_id}",
                        "type": "matched_perception",
                        "length_m": float(traj_info['length']),
                        "duration_s": float(traj_info['duration']),
                        "points_count": int(traj_info['points']),
                        "vehicle_class": int(traj_data['vehicle_class'].iloc[0]) if 'vehicle_class' in traj_data.columns else 0,
                        "avg_speed": float(traj_data['speed'].mean()) if 'speed' in traj_data.columns else 0.0,
                        "start_time": str(traj_data['timestamp'].min()),
                        "end_time": str(traj_data['timestamp'].max())
                    }
                })
            
            matched_geojson = {
                "type": "FeatureCollection",
                "features": matched_features
            }
            
            matched_file = os.path.join(output_dir, f"matched_trajectories_{timestamp}.geojson")
            with open(matched_file, 'w', encoding='utf-8') as f:
                json.dump(matched_geojson, f, ensure_ascii=False, indent=2)
            print(f"匹配轨迹已保存到: {matched_file}")
            
            # 4. 未匹配的感知轨迹
            print("生成未匹配的感知轨迹...")
            unmatched_features = []
            for obj_id, traj_info in results['unmatched'].items():
                # 直接使用原始感知数据的WGS84坐标
                traj_data = traj_info['data']
                traj_wgs84_coords = []
                for _, row in traj_data.iterrows():
                    traj_wgs84_coords.append([float(row['lon']), float(row['lat'])])
                
                unmatched_features.append({
                    "type": "Feature",
                    "geometry": {
                        "type": "LineString",
                        "coordinates": traj_wgs84_coords
                    },
                    "properties": {
                        "id": str(obj_id),
                        "name": f"感知轨迹_{obj_id}",
                        "type": "unmatched_perception",
                        "length_m": float(traj_info['length']),
                        "duration_s": float(traj_info['duration']),
                        "points_count": int(traj_info['points']),
                        "vehicle_class": int(traj_data['vehicle_class'].iloc[0]) if 'vehicle_class' in traj_data.columns else 0,
                        "avg_speed": float(traj_data['speed'].mean()) if 'speed' in traj_data.columns else 0.0,
                        "start_time": str(traj_data['timestamp'].min()),
                        "end_time": str(traj_data['timestamp'].max())
                    }
                })
            
            unmatched_geojson = {
                "type": "FeatureCollection",
                "features": unmatched_features
            }
            
            unmatched_file = os.path.join(output_dir, f"unmatched_trajectories_{timestamp}.geojson")
            with open(unmatched_file, 'w', encoding='utf-8') as f:
                json.dump(unmatched_geojson, f, ensure_ascii=False, indent=2)
            print(f"未匹配轨迹已保存到: {unmatched_file}")
            
            # 5. 生成QGIS项目配置说明
            readme_content = f"""# QGIS轨迹匹配分析结果

## 文件说明

### 数据文件
1. **rtk_trajectory_{timestamp}.geojson** - RTK参考轨迹
   - 红色线条显示
   - 属性：点数、开始时间、结束时间

2. **rtk_corridor_{timestamp}.geojson** - RTK走廊多边形  
   - 半透明蓝色填充
   - 属性：面积、周长

3. **matched_trajectories_{timestamp}.geojson** - 匹配的感知轨迹
   - 绿色线条显示
   - 属性：轨迹ID、长度、持续时间、点数、车辆类型、平均速度、时间范围

4. **unmatched_trajectories_{timestamp}.geojson** - 未匹配的感知轨迹
   - 灰色线条显示
   - 属性：轨迹ID、长度、持续时间、点数、车辆类型、平均速度、时间范围

## QGIS使用说明

### 加载数据
1. 打开QGIS
2. 图层 → 添加图层 → 添加矢量图层
3. 选择对应的.geojson文件
4. 按以下顺序添加图层（从下到上）：
   - rtk_corridor_{timestamp}.geojson（走廊多边形）
   - unmatched_trajectories_{timestamp}.geojson（未匹配轨迹）
   - matched_trajectories_{timestamp}.geojson（匹配轨迹）
   - rtk_trajectory_{timestamp}.geojson（RTK轨迹）

### 样式设置建议
- **RTK轨迹**: 红色，线宽3px
- **走廊多边形**: 蓝色边框，半透明蓝色填充（透明度30%）
- **匹配轨迹**: 绿色，线宽1px
- **未匹配轨迹**: 灰色，线宽1px，透明度50%

### 属性查看
- 右键点击图层 → 打开属性表
- 可以查看每条轨迹的详细信息
- 支持按属性筛选和查询

## 统计信息
- 总轨迹数: {results['stats']['total_trajectories']}
- 匹配轨迹数: {results['stats']['matched_count']} ({results['stats']['match_rate_by_count']:.1f}%)
- 未匹配轨迹数: {results['stats']['unmatched_count']}
- 走廊面积: {corridor_poly.area:.1f} m²

生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
"""
            
            readme_file = os.path.join(output_dir, f"README_{timestamp}.md")
            with open(readme_file, 'w', encoding='utf-8') as f:
                f.write(readme_content)
            
            print(f"\n✅ QGIS数据文件生成完成！")
            print(f"📁 输出目录: {output_dir}")
            print(f"📄 使用说明: {readme_file}")
            print(f"🗺️ 在QGIS中按README说明加载这些GeoJSON文件即可查看分析结果")
            
            return {
                'rtk_file': rtk_file,
                'corridor_file': corridor_file,
                'matched_file': matched_file,
                'unmatched_file': unmatched_file,
                'readme_file': readme_file
            }
            
        except Exception as e:
            print(f"导出QGIS文件时出错: {e}")
            import traceback
            traceback.print_exc()
            return None


def main():
    """主函数"""
    rtk_file = "./data/rtk_part005.txt"
    perception_file = "./data/AJ06993PAJ00115B1.txt"
    
    try:
        matcher = PerceptionTrajectoryMatcher()
        
        # 1. 解析数据
        rtk_df = matcher.parse_rtk_data(rtk_file)
        perception_df = matcher.parse_perception_data(perception_file)
        
        # 2. RTK降采样和生成走廊
        rtk_downsampled, rtk_coords = matcher.downsample_rtk_by_distance(rtk_df, 10.0)
        corridor_poly, prepared_corridor = matcher.generate_corridor_fast(rtk_coords, 25.0, 5.0)
        
        # 3. 过滤感知数据
        perception_filtered = matcher.filter_perception_by_time(perception_df, rtk_df, 3600.0)
        
        # 4. 创建感知轨迹
        trajectories = matcher.create_perception_trajectories(perception_filtered, min_points=3, min_duration=0.5)
        
        # 5. 匹配轨迹到走廊
        results = matcher.match_trajectories_to_corridor(trajectories, prepared_corridor, rtk_coords, rtk_df)
        
        # 6. 输出结果
        matcher.print_matching_results(results)
        
        # 7. 可视化
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        viz_path = f"output/perception_matching_{timestamp}.png"
        os.makedirs("output", exist_ok=True)
        matcher.visualize_matching(rtk_coords, corridor_poly, results, viz_path)
        
        # 8. 导出QGIS文件
        qgis_files = matcher.export_to_qgis(rtk_coords, corridor_poly, results, rtk_df)
        
        return results, qgis_files
        
    except Exception as e:
        print(f"错误: {e}")
        import traceback
        traceback.print_exc()
        return None, None


if __name__ == "__main__":
    results, qgis_files = main()
    if results is not None and qgis_files is not None:
        print("\n🎉 分析完成！")
        print(f"📊 匹配轨迹: {results['stats']['matched_count']}/{results['stats']['total_trajectories']} ({results['stats']['match_rate_by_count']:.1f}%)")
        print(f"🗺️ QGIS文件已生成，可直接在QGIS中查看分析结果") 