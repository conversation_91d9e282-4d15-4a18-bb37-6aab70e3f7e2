#!/usr/bin/env python3
"""
分析长轨迹质量不均匀问题和解决方案
"""

import math
import numpy as np
import matplotlib.pyplot as plt

def simulate_long_trajectory_quality():
    """模拟长轨迹中质量不均匀的情况"""
    print("=== 长轨迹质量分析 ===")
    print()
    
    # 模拟一个30秒轨迹，前10秒质量很好，后20秒质量较差
    trajectory_points = []
    
    # 前10秒：高质量段
    for i in range(100):  # 10秒，10Hz
        distance_error = 0.5 + np.random.normal(0, 0.2)  # 平均0.5米误差
        trajectory_points.append(max(0.1, distance_error))
    
    # 后20秒：低质量段  
    for i in range(200):  # 20秒，10Hz
        distance_error = 3.0 + np.random.normal(0, 1.0)  # 平均3米误差
        trajectory_points.append(max(0.1, distance_error))
    
    # 计算不同评分方法
    print("轨迹段分析:")
    print("- 前10秒：高质量段，平均距离误差 ~0.5米")
    print("- 后20秒：低质量段，平均距离误差 ~3.0米")
    print("- 总时长：30秒")
    print()
    
    # 传统平均评分
    def distance_to_score(distance):
        return math.exp(-distance / 5.0)  # 5米衰减距离
    
    all_scores = [distance_to_score(d) for d in trajectory_points]
    avg_score = np.mean(all_scores)
    
    # 高质量段评分
    high_quality_scores = all_scores[:100]
    high_quality_avg = np.mean(high_quality_scores)
    
    # 低质量段评分
    low_quality_scores = all_scores[100:]
    low_quality_avg = np.mean(low_quality_scores)
    
    print(f"传统平均评分: {avg_score:.3f}")
    print(f"高质量段评分: {high_quality_avg:.3f}")
    print(f"低质量段评分: {low_quality_avg:.3f}")
    print()
    
    print("问题:")
    print(f"- 整体平均分({avg_score:.3f})被低质量段严重拖累")
    print(f"- 高质量段({high_quality_avg:.3f})本身完全可以匹配")
    print(f"- 如果只看前10秒，这是一个完美的短轨迹")
    print()
    
    return trajectory_points, high_quality_avg, low_quality_avg, avg_score

def propose_peak_quality_scoring():
    """提出峰值质量评分方案"""
    print("=== 峰值质量评分方案 ===")
    print()
    
    print("核心思想：长轨迹只要有一段高质量区间，就认为匹配成功")
    print()
    
    def peak_quality_scoring(trajectory_points, window_size=50):
        """
        峰值质量评分：寻找最佳质量窗口
        window_size: 窗口大小（点数），对应5秒@10Hz
        """
        def distance_to_score(distance):
            return math.exp(-distance / 5.0)
        
        scores = [distance_to_score(d) for d in trajectory_points]
        
        if len(scores) < window_size:
            return np.mean(scores), 0, len(scores)-1
        
        # 滑动窗口寻找最佳质量段
        best_score = 0
        best_start = 0
        best_end = window_size - 1
        
        for i in range(len(scores) - window_size + 1):
            window_score = np.mean(scores[i:i+window_size])
            if window_score > best_score:
                best_score = window_score
                best_start = i
                best_end = i + window_size - 1
        
        return best_score, best_start, best_end
    
    # 测试不同窗口大小
    trajectory_points, _, _, avg_score = simulate_long_trajectory_quality()
    
    print("不同窗口大小的峰值质量评分:")
    print("窗口大小 | 对应时长 | 峰值评分 | 最佳区间 | vs平均评分")
    print("-" * 60)
    
    window_sizes = [30, 50, 100, 150]  # 对应3s, 5s, 10s, 15s
    
    for window_size in window_sizes:
        peak_score, start_idx, end_idx = peak_quality_scoring(trajectory_points, window_size)
        duration = window_size / 10  # 10Hz采样
        improvement = peak_score - avg_score
        
        print(f"{window_size:8d} | {duration:7.1f}s | {peak_score:8.3f} | "
              f"{start_idx:3d}-{end_idx:3d} | +{improvement:.3f}")
    
    print()
    return peak_quality_scoring

def propose_hybrid_scoring():
    """提出混合评分方案"""
    print("=== 混合评分方案 ===")
    print()
    
    print("核心思想：结合峰值质量和整体稳定性")
    print("公式: final_score = peak_quality * α + avg_quality * β + duration_bonus * γ")
    print()
    
    def hybrid_scoring(trajectory_points, duration, max_duration=30):
        """混合评分算法"""
        def distance_to_score(distance):
            return math.exp(-distance / 5.0)
        
        scores = [distance_to_score(d) for d in trajectory_points]
        
        # 1. 峰值质量（最佳5秒窗口）
        window_size = min(50, len(scores))  # 5秒窗口
        if len(scores) >= window_size:
            peak_score = 0
            for i in range(len(scores) - window_size + 1):
                window_score = np.mean(scores[i:i+window_size])
                peak_score = max(peak_score, window_score)
        else:
            peak_score = np.mean(scores)
        
        # 2. 整体平均质量
        avg_quality = np.mean(scores)
        
        # 3. 时长奖励（但有上限）
        duration_ratio = min(duration / max_duration, 1.0)
        duration_bonus = math.sqrt(duration_ratio)  # 平方根避免过度奖励
        
        # 4. 自适应权重
        if peak_score > 0.8:  # 高峰值质量
            alpha, beta, gamma = 0.7, 0.2, 0.1  # 主要看峰值
        elif peak_score > 0.6:  # 中等峰值质量
            alpha, beta, gamma = 0.5, 0.3, 0.2  # 平衡
        else:  # 低峰值质量
            alpha, beta, gamma = 0.3, 0.4, 0.3  # 更看重整体和时长
        
        final_score = peak_score * alpha + avg_quality * beta + duration_bonus * gamma
        
        return final_score, peak_score, avg_quality, duration_bonus, (alpha, beta, gamma)
    
    # 测试不同情况
    test_cases = [
        # (前段质量, 后段质量, 前段时长, 后段时长, 描述)
        (0.9, 0.3, 10, 20, "前好后差长轨迹"),
        (0.9, 0.9, 5, 0, "短高质量轨迹"),
        (0.6, 0.6, 15, 15, "中等质量长轨迹"),
        (0.3, 0.9, 10, 20, "前差后好长轨迹"),
        (0.5, 0.5, 30, 0, "中等质量超长轨迹"),
    ]
    
    print("测试不同轨迹类型:")
    print("类型           | 峰值质量 | 平均质量 | 时长奖励 | 权重(α,β,γ) | 最终评分")
    print("-" * 80)
    
    for front_q, back_q, front_dur, back_dur, desc in test_cases:
        # 模拟轨迹点
        total_duration = front_dur + back_dur
        front_points = int(front_dur * 10)  # 10Hz
        back_points = int(back_dur * 10)
        
        trajectory = []
        # 前段
        for _ in range(front_points):
            distance = -math.log(front_q) * 5.0 + np.random.normal(0, 0.5)
            trajectory.append(max(0.1, distance))
        # 后段
        for _ in range(back_points):
            distance = -math.log(back_q) * 5.0 + np.random.normal(0, 0.5)
            trajectory.append(max(0.1, distance))
        
        if trajectory:
            final_score, peak_score, avg_quality, duration_bonus, weights = hybrid_scoring(
                trajectory, total_duration
            )
            
            print(f"{desc:14s} | {peak_score:8.3f} | {avg_quality:8.3f} | "
                  f"{duration_bonus:8.3f} | {weights[0]:.1f},{weights[1]:.1f},{weights[2]:.1f} | "
                  f"{final_score:8.3f}")
    
    print()

def propose_segment_based_scoring():
    """提出基于分段的评分方案"""
    print("=== 分段评分方案 ===")
    print()
    
    print("核心思想：将长轨迹分段评估，只要有一段达标就通过")
    print()
    
    def segment_based_scoring(trajectory_points, segment_duration=5):
        """
        分段评分：将轨迹分成多个段，评估每段质量
        segment_duration: 段长度（秒）
        """
        def distance_to_score(distance):
            return math.exp(-distance / 5.0)
        
        scores = [distance_to_score(d) for d in trajectory_points]
        segment_size = int(segment_duration * 10)  # 10Hz采样
        
        segments = []
        for i in range(0, len(scores), segment_size):
            segment_scores = scores[i:i+segment_size]
            if len(segment_scores) >= segment_size // 2:  # 至少一半长度
                segment_avg = np.mean(segment_scores)
                segments.append({
                    'start_idx': i,
                    'end_idx': i + len(segment_scores) - 1,
                    'duration': len(segment_scores) / 10,
                    'quality': segment_avg
                })
        
        if not segments:
            return 0, []
        
        # 找到最佳段
        best_segment = max(segments, key=lambda x: x['quality'])
        
        # 计算最终评分：最佳段质量 + 段数奖励
        segment_count_bonus = min(len(segments) / 6, 1.0) * 0.1  # 最多6段，每段+0.017分
        final_score = best_segment['quality'] + segment_count_bonus
        
        return final_score, segments, best_segment
    
    # 测试分段评分
    trajectory_points, _, _, _ = simulate_long_trajectory_quality()
    
    final_score, segments, best_segment = segment_based_scoring(trajectory_points)
    
    print(f"轨迹分段结果:")
    print(f"总段数: {len(segments)}")
    print(f"最佳段: 第{segments.index(best_segment)+1}段")
    print(f"最佳段质量: {best_segment['quality']:.3f}")
    print(f"最佳段时间: {best_segment['start_idx']/10:.1f}s - {best_segment['end_idx']/10:.1f}s")
    print(f"最终评分: {final_score:.3f}")
    print()
    
    print("各段质量分布:")
    for i, seg in enumerate(segments):
        quality_level = "优秀" if seg['quality'] > 0.8 else "良好" if seg['quality'] > 0.6 else "一般" if seg['quality'] > 0.4 else "较差"
        print(f"第{i+1}段: {seg['start_idx']/10:.1f}s-{seg['end_idx']/10:.1f}s, 质量={seg['quality']:.3f} ({quality_level})")
    
    print()

if __name__ == "__main__":
    simulate_long_trajectory_quality()
    peak_quality_func = propose_peak_quality_scoring()
    propose_hybrid_scoring()
    propose_segment_based_scoring()
