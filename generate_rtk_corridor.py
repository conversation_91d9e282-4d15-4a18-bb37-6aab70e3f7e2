#!/usr/bin/env python3
"""
RTK轨迹多边形走廊生成与可视化脚本
根据 ai/rtk_perception_candidate_extraction.md 中的方案实现
"""

import sys
import os
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
from matplotlib.patches import Polygon as MPLPolygon
from datetime import datetime
import warnings
import pickle
import json
warnings.filterwarnings('ignore')

# 设置matplotlib中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

# 添加core模块到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'core'))

try:
    from shapely.geometry import Polygon, LineString
    from shapely.ops import unary_union
    import shapely.prepared
    from shapely import wkt
    SHAPELY_AVAILABLE = True
except ImportError:
    print("警告: 未安装shapely，将使用简化实现")
    SHAPELY_AVAILABLE = False

from core.data_processing.readers.nmea_reader import NMEAReader
from core.utils.geo_utils import GeoUtils


class RTKCorridorGenerator:
    """RTK轨迹走廊生成器"""
    
    def __init__(self, long_buffer=25.0, lat_buffer=5.0):
        """
        初始化走廊生成器
        
        Args:
            long_buffer: 纵向缓冲距离(米)
            lat_buffer: 横向缓冲距离(米)
        """
        self.long_buffer = long_buffer
        self.lat_buffer = lat_buffer
        self.geo_utils = GeoUtils()
    
    def parse_rtk_data(self, file_path: str) -> pd.DataFrame:
        """解析RTK NMEA数据"""
        print(f"正在解析RTK数据: {file_path}")
        
        # 使用NMEA读取器
        reader = NMEAReader(file_path)
        raw_records = reader.read()
        
        # 过滤RMC记录（包含完整的位置、时间、航向信息）
        rmc_records = []
        for record in raw_records:
            if record and record.get('type') == 'RMC':
                rmc_records.append(record)
        
        if not rmc_records:
            raise ValueError("未找到有效的RMC记录")
        
        print(f"找到 {len(rmc_records)} 条RMC记录")
        
        # 转换为DataFrame
        df = pd.DataFrame(rmc_records)
        df['timestamp'] = pd.to_datetime(df['timestamp'])
        df = df.sort_values('timestamp').reset_index(drop=True)
        
        # 数据清洗
        df = df.dropna(subset=['lat', 'lon', 'heading'])
        
        print(f"清洗后剩余 {len(df)} 条有效记录")
        return df
    
    def downsample_rtk(self, df: pd.DataFrame, interval_meters=10.0) -> pd.DataFrame:
        """RTK轨迹按距离降采样"""
        print(f"正在按距离降采样RTK数据，间隔: {interval_meters}米")
        
        if len(df) < 2:
            return df
        
        # 先转换为UTM坐标计算距离
        coords = []
        for _, row in df.iterrows():
            x, y, zone = self.geo_utils.wgs84_to_utm(row['lat'], row['lon'])
            coords.append([x, y])
        coords = np.array(coords)
        
        # 计算累积距离
        distances = np.zeros(len(coords))
        for i in range(1, len(coords)):
            distances[i] = distances[i-1] + np.linalg.norm(coords[i] - coords[i-1])
        
        # 按距离间隔采样
        sampled_indices = [0]  # 总是保留第一个点
        current_distance = 0
        
        for i in range(1, len(distances)):
            if distances[i] >= current_distance + interval_meters:
                sampled_indices.append(i)
                current_distance = distances[i]
        
        # 总是保留最后一个点
        if sampled_indices[-1] != len(df) - 1:
            sampled_indices.append(len(df) - 1)
        
        downsampled = df.iloc[sampled_indices].reset_index(drop=True)
        
        # 计算实际的平均采样间隔
        if len(sampled_indices) > 1:
            total_distance = distances[-1]
            avg_interval = total_distance / (len(sampled_indices) - 1)
            print(f"降采样后剩余 {len(downsampled)} 个点，平均间隔: {avg_interval:.1f}米")
        else:
            print(f"降采样后剩余 {len(downsampled)} 个点")
        
        return downsampled
    
    def convert_to_utm(self, df: pd.DataFrame) -> np.ndarray:
        """将经纬度转换为UTM坐标"""
        print("正在转换坐标系统到UTM...")
        
        coords = []
        for _, row in df.iterrows():
            x, y, zone = self.geo_utils.wgs84_to_utm(row['lat'], row['lon'])
            coords.append([x, y])
        
        return np.array(coords)
    
    def generate_corridor_polygon(self, coords: np.ndarray) -> 'Polygon':
        """生成走廊多边形"""
        print(f"正在生成走廊多边形，纵向±{self.long_buffer}m，横向±{self.lat_buffer}m")
        
        if len(coords) < 2:
            raise ValueError("坐标点数量不足，无法生成走廊")
        
        rectangles = []
        
        for i in range(len(coords) - 1):
            p0 = coords[i]
            p1 = coords[i + 1]
            
            # 计算方向向量和垂直向量
            direction = p1 - p0
            length = np.linalg.norm(direction)
            
            if length < 1e-6:  # 跳过距离过近的点
                continue
                
            u = direction / length  # 单位方向向量
            n = np.array([-u[1], u[0]])  # 垂直向量
            
            # 改进：所有段都略微外延，确保重叠
            extra_buffer = self.long_buffer * 0.3  # 额外缓冲避免缝隙
            
            # 起止段纵向外延
            if i == 0:
                p0_ext = p0 - self.long_buffer * u
            else:
                p0_ext = p0 - extra_buffer * u  # 向前略微外延
                
            if i == len(coords) - 2:
                p1_ext = p1 + self.long_buffer * u
            else:
                p1_ext = p1 + extra_buffer * u  # 向后略微外延
            
            # 计算四个角点
            q0 = p0_ext + self.lat_buffer * n
            q1 = p0_ext - self.lat_buffer * n
            q2 = p1_ext - self.lat_buffer * n
            q3 = p1_ext + self.lat_buffer * n
            
            if SHAPELY_AVAILABLE:
                rect = Polygon([q0, q1, q2, q3])
                if rect.is_valid:  # 只添加有效的矩形
                    rectangles.append(rect)
            else:
                # 简化实现：只返回角点列表
                rectangles.append([q0, q1, q2, q3])
        
        if SHAPELY_AVAILABLE:
            # 使用shapely合并所有矩形
            if not rectangles:
                raise ValueError("未生成有效的走廊矩形")
                
            corridor = unary_union(rectangles)
            if hasattr(corridor, 'buffer'):
                corridor = corridor.buffer(0)  # 清理几何缝隙
                
            # 验证结果
            if not corridor.is_valid:
                print("警告: 生成的走廊多边形无效，尝试修复...")
                corridor = corridor.buffer(0.1).buffer(-0.1)  # 尝试修复
                
            print(f"✅ 走廊多边形生成完成，面积: {corridor.area:.1f}m², 周长: {corridor.length:.1f}m")
            return corridor
        else:
            # 简化实现：返回所有矩形
            return rectangles
    
    def generate_corridor_polygon_fast(self, coords: np.ndarray) -> 'Polygon':
        """生成走廊多边形 - 快速一次buffer方法"""
        print(f"正在生成走廊多边形（快速方法），纵向±{self.long_buffer}m，横向±{self.lat_buffer}m")
        
        if len(coords) < 2:
            raise ValueError("坐标点数量不足，无法生成走廊")
        
        if not SHAPELY_AVAILABLE:
            raise ImportError("快速方法需要Shapely支持")
        
        # 1) 计算首尾方向向量并各延长
        u_head = (coords[1] - coords[0])
        u_head = u_head / np.linalg.norm(u_head)
        
        u_tail = (coords[-1] - coords[-2])
        u_tail = u_tail / np.linalg.norm(u_tail)
        
        head_pt = coords[0] - self.long_buffer * u_head
        tail_pt = coords[-1] + self.long_buffer * u_tail
        
        # 2) 拼接延长点后一次buffer
        ext_coords = np.vstack([head_pt, coords, tail_pt])
        
        try:
            corridor = LineString(ext_coords).buffer(
                self.lat_buffer,
                cap_style=2,   # Flat，两端平口正好停在延长点处
                join_style=2   # Miter，拐弯直角拼接
            ).buffer(0)        # 清理几何
            
            # 验证结果
            if not corridor.is_valid:
                print("警告: 生成的走廊多边形无效，尝试修复...")
                corridor = corridor.buffer(0.1).buffer(-0.1)
            
            print(f"✅ 快速走廊多边形生成完成，面积: {corridor.area:.1f}m², 周长: {corridor.length:.1f}m")
            return corridor
            
        except Exception as e:
            print(f"快速方法失败: {e}")
            print("回退到传统方法...")
            return self.generate_corridor_polygon(coords)
    
    def compare_methods(self, coords: np.ndarray) -> dict:
        """对比传统方法和快速方法的性能与结果"""
        print("\n🔬 开始对比传统方法 vs 快速方法...")
        
        import time
        results = {}
        
        # 测试传统方法
        print("\n测试传统方法...")
        start_time = time.time()
        corridor_traditional = self.generate_corridor_polygon(coords)
        traditional_time = time.time() - start_time
        
        results['traditional'] = {
            'time': traditional_time,
            'area': corridor_traditional.area,
            'perimeter': corridor_traditional.length,
            'is_valid': corridor_traditional.is_valid,
            'geometry_type': corridor_traditional.geom_type,
            'boundary_points': len(list(corridor_traditional.exterior.coords)) if hasattr(corridor_traditional, 'exterior') else 0
        }
        
        # 测试快速方法
        print("\n测试快速方法...")
        start_time = time.time()
        corridor_fast = self.generate_corridor_polygon_fast(coords)
        fast_time = time.time() - start_time
        
        results['fast'] = {
            'time': fast_time,
            'area': corridor_fast.area,
            'perimeter': corridor_fast.length,
            'is_valid': corridor_fast.is_valid,
            'geometry_type': corridor_fast.geom_type,
            'boundary_points': len(list(corridor_fast.exterior.coords)) if hasattr(corridor_fast, 'exterior') else 0
        }
        
        # 几何差异分析
        if SHAPELY_AVAILABLE:
            try:
                # 计算两个多边形的差异
                intersection = corridor_traditional.intersection(corridor_fast)
                union = corridor_traditional.union(corridor_fast)
                
                overlap_ratio = intersection.area / union.area if union.area > 0 else 0
                area_diff_ratio = abs(corridor_traditional.area - corridor_fast.area) / corridor_traditional.area
                
                results['comparison'] = {
                    'speed_improvement': traditional_time / fast_time if fast_time > 0 else float('inf'),
                    'overlap_ratio': overlap_ratio,
                    'area_difference_ratio': area_diff_ratio,
                    'perimeter_difference': abs(corridor_traditional.length - corridor_fast.length)
                }
            except Exception as e:
                results['comparison'] = {'error': str(e)}
        
        # 输出对比结果
        print(f"\n📊 性能对比结果:")
        print(f"  传统方法耗时: {traditional_time:.4f}s")
        print(f"  快速方法耗时: {fast_time:.4f}s")
        print(f"  速度提升: {results['comparison'].get('speed_improvement', 0):.1f}x")
        
        print(f"\n📐 几何对比:")
        print(f"  传统方法面积: {results['traditional']['area']:.1f}m²")
        print(f"  快速方法面积: {results['fast']['area']:.1f}m²")
        print(f"  面积差异比例: {results['comparison'].get('area_difference_ratio', 0)*100:.2f}%")
        print(f"  几何重叠率: {results['comparison'].get('overlap_ratio', 0)*100:.1f}%")
        
        print(f"\n✅ 两种方法有效性:")
        print(f"  传统方法: {'✅' if results['traditional']['is_valid'] else '❌'}")
        print(f"  快速方法: {'✅' if results['fast']['is_valid'] else '❌'}")
        
        return results, corridor_traditional, corridor_fast
    
    def save_corridor_data(self, corridor, rtk_coords: np.ndarray, rtk_df: pd.DataFrame, 
                          output_dir: str = "output") -> dict:
        """保存走廊多边形数据到多种格式"""
        print("正在保存多边形数据...")
        
        os.makedirs(output_dir, exist_ok=True)
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        saved_files = {}
        
        if SHAPELY_AVAILABLE:
            # 1. 保存为Shapely pickle格式（最完整）
            pickle_path = os.path.join(output_dir, f"rtk_corridor_{timestamp}.pkl")
            with open(pickle_path, 'wb') as f:
                corridor_data = {
                    'corridor_polygon': corridor,
                    'rtk_coords_utm': rtk_coords,
                    'rtk_dataframe': rtk_df,
                    'parameters': {
                        'long_buffer': self.long_buffer,
                        'lat_buffer': self.lat_buffer
                    },
                    'metadata': {
                        'created_at': datetime.now().isoformat(),
                        'corridor_area': corridor.area,
                        'corridor_length': corridor.length,
                        'rtk_points': len(rtk_coords)
                    }
                }
                pickle.dump(corridor_data, f)
            saved_files['pickle'] = pickle_path
            print(f"  Shapely pickle格式: {pickle_path}")
            
            # 2. 保存为WKT格式
            wkt_path = os.path.join(output_dir, f"rtk_corridor_{timestamp}.wkt")
            with open(wkt_path, 'w', encoding='utf-8') as f:
                f.write(corridor.wkt)
            saved_files['wkt'] = wkt_path
            print(f"  WKT格式: {wkt_path}")
            
            # 3. 保存为GeoJSON格式
            geojson_path = os.path.join(output_dir, f"rtk_corridor_{timestamp}.geojson")
            geojson_data = {
                "type": "FeatureCollection",
                "features": [
                    {
                        "type": "Feature",
                        "properties": {
                            "name": "RTK_Corridor",
                            "long_buffer": self.long_buffer,
                            "lat_buffer": self.lat_buffer,
                            "area_m2": corridor.area,
                            "length_m": corridor.length,
                            "created_at": datetime.now().isoformat()
                        },
                        "geometry": {
                            "type": "Polygon",
                            "coordinates": [list(corridor.exterior.coords)] if hasattr(corridor, 'exterior') else []
                        }
                    }
                ]
            }
            
            with open(geojson_path, 'w', encoding='utf-8') as f:
                json.dump(geojson_data, f, indent=2, ensure_ascii=False)
            saved_files['geojson'] = geojson_path
            print(f"  GeoJSON格式: {geojson_path}")
            
            # 4. 保存边界坐标为CSV
            if hasattr(corridor, 'exterior'):
                coords_csv_path = os.path.join(output_dir, f"rtk_corridor_coords_{timestamp}.csv")
                boundary_coords = list(corridor.exterior.coords)
                coords_df = pd.DataFrame(boundary_coords, columns=['utm_x', 'utm_y'])
                coords_df.to_csv(coords_csv_path, index=False)
                saved_files['coords_csv'] = coords_csv_path
                print(f"  边界坐标CSV: {coords_csv_path}")
        
        # 5. 保存RTK轨迹数据
        rtk_csv_path = os.path.join(output_dir, f"rtk_trajectory_{timestamp}.csv")
        rtk_output = rtk_df.copy()
        rtk_output['utm_x'] = rtk_coords[:, 0]
        rtk_output['utm_y'] = rtk_coords[:, 1]
        rtk_output.to_csv(rtk_csv_path, index=False)
        saved_files['rtk_csv'] = rtk_csv_path
        print(f"  RTK轨迹CSV: {rtk_csv_path}")
        
        # 6. 保存元数据JSON
        metadata_path = os.path.join(output_dir, f"rtk_corridor_metadata_{timestamp}.json")
        metadata = {
            "generation_info": {
                "created_at": datetime.now().isoformat(),
                "parameters": {
                    "long_buffer_m": self.long_buffer,
                    "lat_buffer_m": self.lat_buffer
                }
            },
            "rtk_data": {
                "total_points": len(rtk_df),
                "time_span_seconds": (rtk_df['timestamp'].iloc[-1] - rtk_df['timestamp'].iloc[0]).total_seconds(),
                "start_time": rtk_df['timestamp'].iloc[0].isoformat(),
                "end_time": rtk_df['timestamp'].iloc[-1].isoformat(),
                "avg_speed_ms": float(rtk_df['speed'].mean()),
                "max_speed_ms": float(rtk_df['speed'].max())
            },
            "corridor_stats": {
                "area_m2": float(corridor.area) if SHAPELY_AVAILABLE and hasattr(corridor, 'area') else None,
                "perimeter_m": float(corridor.length) if SHAPELY_AVAILABLE and hasattr(corridor, 'length') else None,
                "boundary_points": len(list(corridor.exterior.coords)) if SHAPELY_AVAILABLE and hasattr(corridor, 'exterior') else None
            },
            "coordinate_system": "UTM",
            "files_generated": saved_files
        }
        
        with open(metadata_path, 'w', encoding='utf-8') as f:
            json.dump(metadata, f, indent=2, ensure_ascii=False)
        saved_files['metadata'] = metadata_path
        print(f"  元数据JSON: {metadata_path}")
        
        return saved_files
    
    def load_corridor_data(self, pickle_path: str) -> dict:
        """从pickle文件加载走廊数据"""
        print(f"正在加载走廊数据: {pickle_path}")
        
        with open(pickle_path, 'rb') as f:
            data = pickle.load(f)
        
        print("✅ 走廊数据加载完成")
        return data
    
    def visualize_corridor(self, rtk_coords: np.ndarray, corridor, rtk_df: pd.DataFrame, 
                          save_path: str = None):
        """可视化走廊多边形"""
        print("正在生成可视化...")
        
        fig, ax = plt.subplots(1, 1, figsize=(12, 10))
        
        # 绘制走廊多边形 - 修复：只绘制合并后的整体多边形
        if SHAPELY_AVAILABLE:
            self._draw_shapely_polygon(ax, corridor)
        else:
            # 简化实现：绘制所有矩形
            for rect in corridor:
                rect_closed = np.vstack([rect, rect[0]])  # 闭合矩形
                ax.fill(rect_closed[:, 0], rect_closed[:, 1], alpha=0.3, color='lightblue')
            ax.plot([], [], 'b-', linewidth=1, alpha=0.7, label='RTK走廊 (±25m×±5m)')
        
        # 绘制RTK轨迹
        ax.plot(rtk_coords[:, 0], rtk_coords[:, 1], 'r-', linewidth=2, label='RTK轨迹')
        ax.scatter(rtk_coords[::10, 0], rtk_coords[::10, 1], c='red', s=30, alpha=0.7, zorder=5)
        
        # 标记起点和终点
        ax.scatter(rtk_coords[0, 0], rtk_coords[0, 1], c='green', s=100, 
                  marker='o', label='起点', zorder=6)
        ax.scatter(rtk_coords[-1, 0], rtk_coords[-1, 1], c='orange', s=100, 
                  marker='s', label='终点', zorder=6)
        
        # 添加方向箭头（每10个点一个）
        for i in range(0, len(rtk_coords)-1, 10):
            dx = rtk_coords[i+1, 0] - rtk_coords[i, 0]
            dy = rtk_coords[i+1, 1] - rtk_coords[i, 1]
            ax.arrow(rtk_coords[i, 0], rtk_coords[i, 1], dx*0.5, dy*0.5,
                    head_width=2, head_length=3, fc='red', ec='red', alpha=0.6)
        
        # 设置图形属性
        ax.set_xlabel('UTM X (米)', fontsize=12)
        ax.set_ylabel('UTM Y (米)', fontsize=12)
        ax.set_title(f'RTK轨迹走廊可视化\n'
                    f'数据点数: {len(rtk_df)}, 时间跨度: {rtk_df["timestamp"].iloc[0]} - {rtk_df["timestamp"].iloc[-1]}', 
                    fontsize=14)
        ax.legend(fontsize=10)
        ax.grid(True, alpha=0.3)
        ax.set_aspect('equal')
        
        # 添加统计信息
        total_distance = 0
        for i in range(len(rtk_coords)-1):
            total_distance += np.linalg.norm(rtk_coords[i+1] - rtk_coords[i])
        
        info_text = f'轨迹总长度: {total_distance:.1f}m\n'
        info_text += f'平均速度: {rtk_df["speed"].mean():.1f}m/s\n'
        info_text += f'走廊参数: 纵向±{self.long_buffer}m, 横向±{self.lat_buffer}m'
        
        # 添加多边形验证信息
        if SHAPELY_AVAILABLE:
            info_text += f'\n多边形有效性: {"✅" if corridor.is_valid else "❌"}'
            if hasattr(corridor, 'area'):
                info_text += f'\n走廊面积: {corridor.area:.1f}m²'
        
        ax.text(0.02, 0.98, info_text, transform=ax.transAxes, fontsize=10,
                verticalalignment='top', bbox=dict(boxstyle='round', facecolor='wheat', alpha=0.8))
        
        plt.tight_layout()
        
        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
            print(f"可视化结果已保存到: {save_path}")
        
        plt.show()
    
    def _draw_shapely_polygon(self, ax, geometry):
        """绘制Shapely几何对象，确保无视觉缝隙"""
        if geometry.geom_type == 'Polygon':
            # 单个多边形
            x, y = geometry.exterior.xy
            ax.fill(x, y, alpha=0.3, color='lightblue', edgecolor='blue', 
                   linewidth=1, label='RTK走廊 (±25m×±5m)')
            
            # 绘制内部孔洞（如果有）
            for interior in geometry.interiors:
                x, y = interior.xy
                ax.fill(x, y, alpha=1.0, color='white', edgecolor='blue', linewidth=1)
                
        elif geometry.geom_type == 'MultiPolygon':
            # 多个多边形
            for i, poly in enumerate(geometry.geoms):
                x, y = poly.exterior.xy
                label = 'RTK走廊 (±25m×±5m)' if i == 0 else None
                ax.fill(x, y, alpha=0.3, color='lightblue', edgecolor='blue', 
                       linewidth=1, label=label)
                
                # 绘制内部孔洞
                for interior in poly.interiors:
                    x, y = interior.xy
                    ax.fill(x, y, alpha=1.0, color='white', edgecolor='blue', linewidth=1)
        else:
            print(f"警告: 不支持的几何类型 {geometry.geom_type}")
    
    def diagnose_corridor(self, corridor, rtk_coords: np.ndarray) -> dict:
        """诊断走廊多边形的几何完整性"""
        print("正在诊断走廊多边形...")
        
        diagnosis = {
            'is_valid': False,
            'geometry_type': None,
            'area': 0,
            'perimeter': 0,
            'has_holes': False,
            'num_parts': 0,
            'boundary_points': 0,
            'issues': [],
            'recommendations': []
        }
        
        if not SHAPELY_AVAILABLE:
            diagnosis['issues'].append("Shapely未安装，无法进行完整诊断")
            return diagnosis
        
        try:
            # 基本几何信息
            diagnosis['is_valid'] = corridor.is_valid
            diagnosis['geometry_type'] = corridor.geom_type
            diagnosis['area'] = float(corridor.area)
            diagnosis['perimeter'] = float(corridor.length)
            
            if corridor.geom_type == 'Polygon':
                diagnosis['num_parts'] = 1
                diagnosis['has_holes'] = len(corridor.interiors) > 0
                diagnosis['boundary_points'] = len(list(corridor.exterior.coords))
            elif corridor.geom_type == 'MultiPolygon':
                diagnosis['num_parts'] = len(corridor.geoms)
                diagnosis['has_holes'] = any(len(poly.interiors) > 0 for poly in corridor.geoms)
                diagnosis['boundary_points'] = sum(len(list(poly.exterior.coords)) for poly in corridor.geoms)
            
            # 检查常见问题
            if not corridor.is_valid:
                from shapely.validation import explain_validity
                validity_reason = explain_validity(corridor)
                diagnosis['issues'].append(f"几何无效: {validity_reason}")
                diagnosis['recommendations'].append("尝试使用buffer(0)修复几何")
            
            if diagnosis['area'] <= 0:
                diagnosis['issues'].append("走廊面积为零或负值")
                diagnosis['recommendations'].append("检查RTK点顺序和坐标系统")
            
            if diagnosis['num_parts'] > 1:
                diagnosis['issues'].append(f"走廊被分割为{diagnosis['num_parts']}个独立部分")
                diagnosis['recommendations'].append("考虑增加缓冲距离或减少采样间隔")
            
            if diagnosis['has_holes']:
                diagnosis['issues'].append("走廊内部存在孔洞")
                diagnosis['recommendations'].append("检查RTK轨迹是否有回环或自交")
            
            # 检查走廊与RTK轨迹的关系
            rtk_line = LineString(rtk_coords)
            if not corridor.contains(rtk_line):
                if corridor.intersects(rtk_line):
                    diagnosis['issues'].append("部分RTK轨迹超出走廊范围")
                    diagnosis['recommendations'].append("增加横向缓冲距离")
                else:
                    diagnosis['issues'].append("RTK轨迹完全不在走廊内")
                    diagnosis['recommendations'].append("检查坐标系统和缓冲参数")
            
            # 性能检查
            if diagnosis['boundary_points'] > 10000:
                diagnosis['issues'].append(f"边界点过多({diagnosis['boundary_points']})")
                diagnosis['recommendations'].append("考虑简化多边形以提高性能")
            
        except Exception as e:
            diagnosis['issues'].append(f"诊断过程出错: {str(e)}")
        
        # 输出诊断结果
        print(f"\n🔍 走廊多边形诊断结果:")
        print(f"  几何有效性: {'✅' if diagnosis['is_valid'] else '❌'}")
        print(f"  几何类型: {diagnosis['geometry_type']}")
        print(f"  面积: {diagnosis['area']:.1f}m²")
        print(f"  周长: {diagnosis['perimeter']:.1f}m")
        print(f"  组成部分: {diagnosis['num_parts']}")
        print(f"  边界点数: {diagnosis['boundary_points']}")
        print(f"  内部孔洞: {'是' if diagnosis['has_holes'] else '否'}")
        
        if diagnosis['issues']:
            print(f"\n⚠️ 发现问题:")
            for issue in diagnosis['issues']:
                print(f"    • {issue}")
        
        if diagnosis['recommendations']:
            print(f"\n💡 建议:")
            for rec in diagnosis['recommendations']:
                print(f"    • {rec}")
        
        if not diagnosis['issues']:
            print(f"\n✅ 走廊多边形几何完整，无明显问题")
        
        return diagnosis

    def visualize_comparison(self, rtk_coords: np.ndarray, corridor_traditional, corridor_fast, 
                           rtk_df: pd.DataFrame, comparison_results: dict, save_path: str = None):
        """可视化对比传统方法和快速方法的结果"""
        print("正在生成对比可视化...")
        
        fig, axes = plt.subplots(1, 3, figsize=(18, 6))
        
        # 子图1: 传统方法
        ax1 = axes[0]
        self._draw_shapely_polygon(ax1, corridor_traditional)
        ax1.plot(rtk_coords[:, 0], rtk_coords[:, 1], 'r-', linewidth=2, label='RTK轨迹')
        ax1.scatter(rtk_coords[0, 0], rtk_coords[0, 1], c='green', s=100, marker='o', label='起点', zorder=6)
        ax1.scatter(rtk_coords[-1, 0], rtk_coords[-1, 1], c='orange', s=100, marker='s', label='终点', zorder=6)
        ax1.set_title(f'传统方法\n耗时: {comparison_results["traditional"]["time"]:.4f}s\n面积: {comparison_results["traditional"]["area"]:.1f}m²')
        ax1.set_xlabel('UTM X (米)')
        ax1.set_ylabel('UTM Y (米)')
        ax1.legend()
        ax1.grid(True, alpha=0.3)
        ax1.set_aspect('equal')
        
        # 子图2: 快速方法
        ax2 = axes[1]
        self._draw_shapely_polygon(ax2, corridor_fast)
        ax2.plot(rtk_coords[:, 0], rtk_coords[:, 1], 'r-', linewidth=2, label='RTK轨迹')
        ax2.scatter(rtk_coords[0, 0], rtk_coords[0, 1], c='green', s=100, marker='o', label='起点', zorder=6)
        ax2.scatter(rtk_coords[-1, 0], rtk_coords[-1, 1], c='orange', s=100, marker='s', label='终点', zorder=6)
        ax2.set_title(f'快速方法\n耗时: {comparison_results["fast"]["time"]:.4f}s\n面积: {comparison_results["fast"]["area"]:.1f}m²')
        ax2.set_xlabel('UTM X (米)')
        ax2.set_ylabel('UTM Y (米)')
        ax2.legend()
        ax2.grid(True, alpha=0.3)
        ax2.set_aspect('equal')
        
        # 子图3: 重叠对比
        ax3 = axes[2]
        # 绘制两个多边形的重叠区域
        if SHAPELY_AVAILABLE:
            try:
                intersection = corridor_traditional.intersection(corridor_fast)
                difference_trad = corridor_traditional.difference(corridor_fast)
                difference_fast = corridor_fast.difference(corridor_traditional)
                
                # 重叠区域 - 绿色
                if not intersection.is_empty:
                    self._draw_geometry_with_color(ax3, intersection, 'green', 0.6, '重叠区域')
                
                # 传统方法独有 - 红色
                if not difference_trad.is_empty:
                    self._draw_geometry_with_color(ax3, difference_trad, 'red', 0.4, '传统方法独有')
                
                # 快速方法独有 - 蓝色
                if not difference_fast.is_empty:
                    self._draw_geometry_with_color(ax3, difference_fast, 'blue', 0.4, '快速方法独有')
                    
            except Exception as e:
                print(f"重叠分析出错: {e}")
                # 回退：分别绘制两个多边形
                self._draw_geometry_with_color(ax3, corridor_traditional, 'red', 0.3, '传统方法')
                self._draw_geometry_with_color(ax3, corridor_fast, 'blue', 0.3, '快速方法')
        
        ax3.plot(rtk_coords[:, 0], rtk_coords[:, 1], 'k-', linewidth=2, label='RTK轨迹')
        ax3.scatter(rtk_coords[0, 0], rtk_coords[0, 1], c='green', s=100, marker='o', label='起点', zorder=6)
        ax3.scatter(rtk_coords[-1, 0], rtk_coords[-1, 1], c='orange', s=100, marker='s', label='终点', zorder=6)
        
        speed_improvement = comparison_results.get('comparison', {}).get('speed_improvement', 0)
        overlap_ratio = comparison_results.get('comparison', {}).get('overlap_ratio', 0)
        ax3.set_title(f'重叠对比\n速度提升: {speed_improvement:.1f}x\n重叠率: {overlap_ratio*100:.1f}%')
        ax3.set_xlabel('UTM X (米)')
        ax3.set_ylabel('UTM Y (米)')
        ax3.legend()
        ax3.grid(True, alpha=0.3)
        ax3.set_aspect('equal')
        
        plt.tight_layout()
        
        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
            print(f"对比可视化已保存到: {save_path}")
        
        plt.show()
    
    def _draw_geometry_with_color(self, ax, geometry, color, alpha, label):
        """用指定颜色绘制几何对象"""
        if geometry.geom_type == 'Polygon':
            x, y = geometry.exterior.xy
            ax.fill(x, y, alpha=alpha, color=color, label=label)
        elif geometry.geom_type == 'MultiPolygon':
            for i, poly in enumerate(geometry.geoms):
                x, y = poly.exterior.xy
                current_label = label if i == 0 else None
                ax.fill(x, y, alpha=alpha, color=color, label=current_label)


def main():
    """主函数"""
    # 配置参数
    rtk_file = "./data/rtk_part005.txt"
    long_buffer = 25.0  # 纵向缓冲距离
    lat_buffer = 5.0    # 横向缓冲距离
    downsample_interval = 10.0  # 降采样间隔(米)
    
    try:
        # 创建走廊生成器
        generator = RTKCorridorGenerator(long_buffer, lat_buffer)
        
        # 解析RTK数据
        rtk_df = generator.parse_rtk_data(rtk_file)
        
        # 按距离降采样
        rtk_downsampled = generator.downsample_rtk(rtk_df, downsample_interval)
        
        # 转换坐标系
        rtk_coords = generator.convert_to_utm(rtk_downsampled)
        
        # 生成走廊多边形
        corridor = generator.generate_corridor_polygon(rtk_coords)
        
        # 诊断走廊多边形
        diagnosis = generator.diagnose_corridor(corridor, rtk_coords)
        
        # 对比两种方法的性能
        comparison_results, corridor_traditional, corridor_fast = generator.compare_methods(rtk_coords)
        
        # 保存多边形数据（使用快速方法的结果）
        saved_files = generator.save_corridor_data(corridor_fast, rtk_coords, rtk_downsampled)
        
        # 生成对比可视化
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        comparison_plot_path = f"output/corridor_comparison_{timestamp}.png"
        generator.visualize_comparison(rtk_coords, corridor_traditional, corridor_fast, 
                                     rtk_downsampled, comparison_results, comparison_plot_path)
        
        print("\n" + "="*50)
        print("✅ RTK走廊生成与数据保存完成!")
        print("📁 已保存的文件:")
        for file_type, file_path in saved_files.items():
            print(f"  {file_type}: {file_path}")
        print("="*50)
        
    except Exception as e:
        print(f"❌ 处理失败: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main() 