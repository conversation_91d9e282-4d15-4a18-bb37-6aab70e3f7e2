# 车路协同轨迹匹配系统重构方案 - 代码示例

本文档提供重构方案中关键模块的代码示例，作为实施参考。

## 1. 数据处理模块

### 1.1 读取器接口设计

```python
# core/data_processing/readers/base_reader.py
from abc import ABC, abstractmethod
from typing import List, Dict, Any

class BaseDataReader(ABC):
    """数据读取器抽象基类"""
    
    @abstractmethod
    def read(self, file_path: str) -> List[Dict[str, Any]]:
        """读取原始数据"""
        pass
    
    @abstractmethod
    def detect_format(self, file_path: str) -> bool:
        """检测是否支持该格式"""
        pass
```

### 1.2 NMEA读取器实现

```python
# core/data_processing/readers/nmea_reader.py
from typing import List, Dict, Any
import re
from .base_reader import BaseDataReader

class NMEAReader(BaseDataReader):
    """NMEA格式RTK数据读取器"""
    
    def detect_format(self, file_path: str) -> bool:
        """检测是否为NMEA格式"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                for _ in range(10):  # 检查前10行
                    line = f.readline().strip()
                    if line.startswith('$GP'):
                        return True
            return False
        except Exception:
            return False
    
    def read(self, file_path: str) -> List[Dict[str, Any]]:
        """读取NMEA格式数据"""
        raw_data = []
        
        with open(file_path, 'r', encoding='utf-8') as f:
            for line in f:
                line = line.strip()
                if line.startswith('$GP'):
                    raw_data.append({'raw_sentence': line})
        
        return raw_data
```

### 1.3 解析器接口设计

```python
# core/data_processing/parsers/base_parser.py
from abc import ABC, abstractmethod
from typing import List, Dict, Any

class BaseDataParser(ABC):
    """数据解析器抽象基类"""
    
    @abstractmethod
    def parse(self, raw_data: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """解析原始数据为标准格式"""
        pass
```

### 1.4 NMEA解析器实现

```python
# core/data_processing/parsers/nmea_parser.py
from typing import List, Dict, Any
from datetime import datetime, timezone
import re
from .base_parser import BaseDataParser

class NMEAParser(BaseDataParser):
    """NMEA格式解析器"""
    
    def parse(self, raw_data: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """解析NMEA句为标准RTK格式"""
        parsed_data = []
        
        # 临时存储GGA和RMC数据
        gga_data = {}
        rmc_data = {}
        
        for item in raw_data:
            sentence = item['raw_sentence']
            
            if sentence.startswith('$GPGGA'):
                # 解析GGA句
                gga_parts = sentence.split(',')
                if len(gga_parts) >= 10:
                    time_str = gga_parts[1]
                    lat_str = gga_parts[2]
                    lat_dir = gga_parts[3]
                    lon_str = gga_parts[4]
                    lon_dir = gga_parts[5]
                    
                    # 提取时间
                    if time_str:
                        hour = int(time_str[0:2])
                        minute = int(time_str[2:4])
                        second = float(time_str[4:])
                        
                        # 使用临时日期，后续由RMC补充
                        gga_data['hour'] = hour
                        gga_data['minute'] = minute
                        gga_data['second'] = second
                    
                    # 提取坐标
                    if lat_str and lon_str:
                        lat = float(lat_str[:2]) + float(lat_str[2:]) / 60
                        if lat_dir == 'S':
                            lat = -lat
                            
                        lon = float(lon_str[:3]) + float(lon_str[3:]) / 60
                        if lon_dir == 'W':
                            lon = -lon
                        
                        gga_data['lat'] = lat
                        gga_data['lon'] = lon
            
            elif sentence.startswith('$GPRMC'):
                # 解析RMC句
                rmc_parts = sentence.split(',')
                if len(rmc_parts) >= 10:
                    date_str = rmc_parts[9]
                    speed_str = rmc_parts[7]
                    course_str = rmc_parts[8]
                    
                    # 提取日期
                    if date_str:
                        day = int(date_str[0:2])
                        month = int(date_str[2:4])
                        year = 2000 + int(date_str[4:6])  # 假设是21世纪
                        
                        rmc_data['day'] = day
                        rmc_data['month'] = month
                        rmc_data['year'] = year
                    
                    # 提取速度和航向
                    if speed_str:
                        # 节转换为米/秒
                        speed = float(speed_str) * 0.514444
                        rmc_data['speed'] = speed
                    
                    if course_str:
                        heading = float(course_str)
                        rmc_data['heading'] = heading
            
            # 如果GGA和RMC数据都有了，合并并添加到结果
            if gga_data and rmc_data:
                # 构建完整时间戳
                try:
                    timestamp = datetime(
                        rmc_data['year'], 
                        rmc_data['month'], 
                        rmc_data['day'],
                        gga_data['hour'],
                        gga_data['minute'],
                        int(gga_data['second']),
                        int((gga_data['second'] % 1) * 1000000),
                        tzinfo=timezone.utc
                    )
                    
                    parsed_data.append({
                        'timestamp': timestamp,
                        'lat': gga_data['lat'],
                        'lon': gga_data['lon'],
                        'speed': rmc_data.get('speed', 0.0),
                        'heading': rmc_data.get('heading', 0.0)
                    })
                    
                    # 清空临时数据
                    gga_data = {}
                    rmc_data = {}
                except Exception as e:
                    print(f"解析NMEA数据错误: {e}")
        
        return parsed_data
```

### 1.5 预处理器实现

```python
# core/data_processing/preprocessors/data_preprocessor.py
from typing import List, Dict, Any
from ...models.rtk_point import RTKPoint
from ...models.perception_point import PerceptionPoint
from ...utils.geo_utils import GeoUtils

class DataPreprocessor:
    """数据预处理器"""
    
    def __init__(self, config):
        self.config = config
        self.geo_utils = GeoUtils()
    
    def process_rtk_data(self, parsed_data: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """处理RTK数据"""
        # 时间排序
        sorted_data = sorted(parsed_data, key=lambda x: x['timestamp'])
        
        # 数据验证和清洗
        cleaned_data = self._clean_rtk_data(sorted_data)
        
        return cleaned_data
    
    def process_perception_data(self, parsed_data: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """处理感知数据"""
        # 时间排序
        sorted_data = sorted(parsed_data, key=lambda x: x['timestamp'])
        
        # 数据验证和清洗
        cleaned_data = self._clean_perception_data(sorted_data)
        
        return cleaned_data
    
    def roi_filter(self, rtk_points: List[RTKPoint], perception_points: List[PerceptionPoint]) -> List[PerceptionPoint]:
        """ROI空间过滤"""
        filtered_points = []
        
        for per_point in perception_points:
            # 找到最近的RTK点
            min_distance = float('inf')
            nearest_rtk = None
            
            for rtk_point in rtk_points:
                # 计算时间差
                time_diff = abs((per_point.timestamp - rtk_point.timestamp).total_seconds())
                if time_diff > self.config.roi_time_window:  # 时间窗口
                    continue
                
                # 计算距离
                distance = self.geo_utils.haversine_distance(
                    per_point.lat, per_point.lon,
                    rtk_point.lat, rtk_point.lon
                )
                
                if distance < min_distance:
                    min_distance = distance
                    nearest_rtk = rtk_point
            
            # 检查是否在ROI内
            if nearest_rtk and min_distance <= self.config.roi_distance:
                filtered_points.append(per_point)
        
        return filtered_points
    
    def _clean_rtk_data(self, data: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """清洗RTK数据"""
        cleaned = []
        
        for item in data:
            # 基本验证
            if not all(k in item for k in ['timestamp', 'lat', 'lon']):
                continue
                
            # 坐标范围验证
            if not (-90 <= item['lat'] <= 90 and -180 <= item['lon'] <= 180):
                continue
                
            # 添加默认值
            if 'speed' not in item:
                item['speed'] = 0.0
                
            if 'heading' not in item:
                item['heading'] = 0.0
                
            cleaned.append(item)
            
        return cleaned
    
    def _clean_perception_data(self, data: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """清洗感知数据"""
        # 类似RTK数据的清洗逻辑
        return data
```

## 2. 轨迹匹配模块

### 2.1 匹配器基类

```python
# core/matching/matchers/base_matcher.py
from abc import ABC, abstractmethod
from typing import List, Dict, Any
from ...models.rtk_point import RTKPoint
from ...models.perception_point import PerceptionPoint
from ...models.trajectory_segment import TrajectorySegment

class BaseTrajectoryMatcher(ABC):
    """轨迹匹配器抽象基类"""
    
    def __init__(self, config):
        self.config = config
    
    @abstractmethod
    def match(self, rtk_points: List[RTKPoint], perception_points: List[PerceptionPoint]) -> List[TrajectorySegment]:
        """执行轨迹匹配"""
        pass
    
    @abstractmethod
    def calculate_score(self, segment: TrajectorySegment, rtk_points: List[RTKPoint]) -> float:
        """计算匹配分数"""
        pass
```

### 2.2 轨迹分析器

```python
# core/matching/analyzers/trajectory_analyzer.py
from typing import List, Dict, Any
from datetime import datetime
from ...models.trajectory_segment import TrajectorySegment
from ...models.rtk_point import RTKPoint

class TrajectoryAnalyzer:
    """轨迹分析器"""
    
    def __init__(self, config):
        self.config = config
    
    def analyze(self, chain: List[TrajectorySegment], rtk_points: List[RTKPoint]) -> Dict[str, Any]:
        """执行完整轨迹分析"""
        anomalies = {
            'split_events': [],
            'id_switches': [],
            'missing_gaps': [],
            'rejected_segments': []
        }
        
        # 1. 全局分裂分析
        split_events = self.global_split_analysis(chain)
        anomalies['split_events'] = split_events
        
        # 2. ID切换分析
        id_switches = self.global_id_switch_analysis(chain)
        anomalies['id_switches'] = id_switches
        
        # 3. 漏检分析
        if rtk_points:
            rtk_start_time = rtk_points[0].timestamp
            rtk_end_time = rtk_points[-1].timestamp
            missing_gaps = self.missing_gap_analysis(chain, rtk_start_time, rtk_end_time)
            anomalies['missing_gaps'] = missing_gaps
        
        return anomalies
    
    def global_split_analysis(self, chain: List[TrajectorySegment]) -> List[Dict[str, Any]]:
        """全局分裂分析"""
        split_events = []
        
        # 实现分裂分析逻辑
        
        return split_events
    
    def global_id_switch_analysis(self, chain: List[TrajectorySegment]) -> List[Dict[str, Any]]:
        """全局ID切换分析"""
        id_switches = []
        
        # 实现ID切换分析逻辑
        
        return id_switches
    
    def missing_gap_analysis(self, chain: List[TrajectorySegment], 
                            rtk_start_time: datetime, 
                            rtk_end_time: datetime) -> List[Dict[str, Any]]:
        """漏检分析"""
        missing_gaps = []
        
        # 实现漏检分析逻辑
        
        return missing_gaps
```

## 3. 指标计算模块

### 3.1 指标计算器

```python
# core/metrics/metrics_calculator.py
from typing import List, Dict, Any
from ..models.rtk_point import RTKPoint
from ..models.trajectory_segment import TrajectorySegment

class MetricsCalculator:
    """指标计算器"""
    
    def __init__(self, config):
        self.config = config
    
    def calculate_coverage_metrics(self, rtk_points: List[RTKPoint], matched_chain: List[TrajectorySegment]) -> Dict[str, Any]:
        """计算覆盖率相关指标"""
        rtk_duration = self._calculate_rtk_duration(rtk_points)
        matched_duration = self._calculate_matched_duration(matched_chain)
        
        return {
            'rtk_duration': rtk_duration,
            'matched_duration': matched_duration,
            'coverage_rate': (matched_duration / rtk_duration * 100) if rtk_duration > 0 else 0,
            'segment_count': len(matched_chain)
        }
    
    def calculate_quality_metrics(self, matched_chain: List[TrajectorySegment]) -> Dict[str, Any]:
        """计算质量相关指标"""
        scores = [getattr(seg, 'final_score', 0) for seg in matched_chain]
        avg_score = sum(scores) / len(scores) if scores else 0
        
        return {
            'avg_match_score': avg_score,
            'max_score': max(scores) if scores else 0,
            'min_score': min(scores) if scores else 0,
            'unique_ids': self._get_unique_ids(matched_chain)
        }
    
    def calculate_anomaly_metrics(self, anomalies: Dict[str, Any]) -> Dict[str, Any]:
        """计算异常相关指标"""
        missing_gaps = anomalies.get('missing_gaps', [])
        total_missing_duration = sum(gap.get('duration', 0) for gap in missing_gaps)
        
        return {
            'split_events_count': len(anomalies.get('split_events', [])),
            'id_switches_count': len(anomalies.get('id_switches', [])),
            'missing_gaps_count': len(missing_gaps),
            'total_missing_duration': total_missing_duration
        }
    
    def _calculate_rtk_duration(self, rtk_points: List[RTKPoint]) -> float:
        """计算RTK轨迹总时长(秒)"""
        if not rtk_points or len(rtk_points) < 2:
            return 0.0
            
        return (rtk_points[-1].timestamp - rtk_points[0].timestamp).total_seconds()
    
    def _calculate_matched_duration(self, matched_chain: List[TrajectorySegment]) -> float:
        """计算匹配轨迹总时长(秒)"""
        return sum(seg.duration for seg in matched_chain)
    
    def _get_unique_ids(self, matched_chain: List[TrajectorySegment]) -> List[int]:
        """获取唯一ID列表"""
        return sorted(list(set(seg.id for seg in matched_chain)))
```

---

**文档创建时间**: 2025-07-15
**版本**: v1.0
**状态**: 参考代码
