#!/usr/bin/env python3
"""
完整功能测试脚本
测试gap填充、异常检测和输出生成功能
"""

import os
import sys
import tempfile
import shutil
from datetime import datetime, timedelta
import pandas as pd

# 添加core模块到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(os.path.dirname(__file__)), 'core'))

from data_utils import DataLoader, Config, RTKPoint, PerceptionPoint
from simple_distance_matcher import SimpleDistanceMatcher
from output_generator import OutputGenerator, trim_rtk_by_match_time

def create_test_data():
    """创建测试数据"""
    print("创建测试数据...")
    
    # 创建临时目录
    temp_dir = tempfile.mkdtemp()
    print(f"临时目录: {temp_dir}")
    
    # 创建RTK测试数据
    rtk_data = []
    base_time = datetime(2024, 1, 1, 10, 0, 0)
    
    for i in range(300):  # 30秒，10Hz
        timestamp = base_time + timedelta(seconds=i * 0.1)
        lat = 39.9 + i * 0.0001  # 模拟直线运动
        lon = 116.4 + i * 0.0001
        speed = 10.0 + (i % 20) * 0.1  # 模拟速度变化
        heading = 45.0 + (i % 10) * 0.5  # 模拟航向变化
        
        rtk_data.append({
            'timestamp': timestamp.isoformat() + 'Z',  # 添加UTC标记
            'lat': lat,
            'lon': lon,
            'speed': speed,
            'heading': heading
        })
    
    rtk_df = pd.DataFrame(rtk_data)
    rtk_file = os.path.join(temp_dir, 'rtk_test.csv')
    rtk_df.to_csv(rtk_file, index=False)
    
    # 创建感知测试数据（包含多个ID和间隙）
    perception_data = []
    
    # ID 101781: 0-10秒，20-30秒（中间有10秒gap）
    for i in range(0, 100, 2):  # 0-10秒，5Hz
        timestamp = base_time + timedelta(seconds=i * 0.1)
        lat = 39.9 + i * 0.0001 + 0.0002  # 位置有偏移
        lon = 116.4 + i * 0.0001 + 0.0002
        speed = 10.0 + (i % 20) * 0.1
        heading = 45.0 + (i % 10) * 0.5
        
        perception_data.append({
            'timestamp': timestamp.isoformat() + 'Z',  # 添加UTC标记
            'id': 101781,
            'lat': lat,
            'lon': lon,
            'speed': speed,
            'heading': heading
        })
    
    for i in range(200, 300, 2):  # 20-30秒，5Hz
        timestamp = base_time + timedelta(seconds=i * 0.1)
        lat = 39.9 + i * 0.0001 + 0.0002
        lon = 116.4 + i * 0.0001 + 0.0002
        speed = 10.0 + (i % 20) * 0.1
        heading = 45.0 + (i % 10) * 0.5
        
        perception_data.append({
            'timestamp': timestamp.isoformat() + 'Z',  # 添加UTC标记
            'id': 101781,
            'lat': lat,
            'lon': lon,
            'speed': speed,
            'heading': heading
        })
    
    # ID 101987: 12-18秒（用于gap填充）
    for i in range(120, 180, 2):  # 12-18秒，5Hz
        timestamp = base_time + timedelta(seconds=i * 0.1)
        lat = 39.9 + i * 0.0001 + 0.0001
        lon = 116.4 + i * 0.0001 + 0.0001
        speed = 10.0 + (i % 20) * 0.1
        heading = 45.0 + (i % 10) * 0.5
        
        perception_data.append({
            'timestamp': timestamp.isoformat() + 'Z',  # 添加UTC标记
            'id': 101987,
            'lat': lat,
            'lon': lon,
            'speed': speed,
            'heading': heading
        })
    
    # ID 102156: 10-12秒（重叠，用于分裂检测）
    for i in range(100, 120, 2):  # 10-12秒，5Hz
        timestamp = base_time + timedelta(seconds=i * 0.1)
        lat = 39.9 + i * 0.0001 + 0.0001
        lon = 116.4 + i * 0.0001 + 0.0001
        speed = 10.0 + (i % 20) * 0.1
        heading = 45.0 + (i % 10) * 0.5
        
        perception_data.append({
            'timestamp': timestamp.isoformat() + 'Z',  # 添加UTC标记
            'id': 102156,
            'lat': lat,
            'lon': lon,
            'speed': speed,
            'heading': heading
        })
    
    perception_df = pd.DataFrame(perception_data)
    perception_file = os.path.join(temp_dir, 'perception_test.csv')
    perception_df.to_csv(perception_file, index=False)
    
    return temp_dir, rtk_file, perception_file

def test_complete_pipeline():
    """测试完整的匹配流程"""
    print("=" * 60)
    print("完整功能测试")
    print("=" * 60)
    
    # 创建测试数据
    temp_dir, rtk_file, perception_file = create_test_data()
    
    try:
        # 加载配置
        config = Config()
        
        # 1. 数据加载
        print("1. 数据加载...")
        data_loader = DataLoader(config)
        
        rtk_points = data_loader.load_rtk_csv(rtk_file)
        perception_points = data_loader.load_perception_csv(perception_file)
        
        print(f"   RTK轨迹点: {len(rtk_points)}")
        print(f"   感知数据点: {len(perception_points)}")
        
        # 2. 时间同步
        print("2. 时间同步...")
        rtk_points = data_loader.sync_rtk_time(rtk_points)
        perception_points = data_loader.sync_perception_time(perception_points)
        
        rtk_start = rtk_points[0].timestamp
        rtk_end = rtk_points[-1].timestamp
        
        print(f"   RTK时间范围: {rtk_start} ~ {rtk_end}")
        
        # 3. 轨迹匹配
        print("3. 轨迹匹配...")
        matcher = SimpleDistanceMatcher(config, rtk_points)
        
        # ROI过滤
        filtered_perception = matcher.filter_perception_points(perception_points)
        print(f"   ROI过滤后: {len(filtered_perception)} 点")
        
        # 轨迹段构建
        segments = matcher.build_segments(filtered_perception)
        print(f"   轨迹段数量: {len(segments)}")
        
        for i, seg in enumerate(segments):
            print(f"      段{i+1}: ID={seg.id}, 时长={seg.duration:.1f}s, 点数={len(seg.points)}")
        
        # 核心链构建
        core_chain = matcher.build_core_chain(segments)
        print(f"   核心链段数: {len(core_chain)}")
        
        for i, seg in enumerate(core_chain):
            print(f"      核心段{i+1}: ID={seg.id}, 时长={seg.duration:.1f}s, 分数={getattr(seg, 'final_score', 0):.3f}")
        
        # 4. 异常检测和汇总
        print("4. 异常检测和汇总...")
        anomalies = matcher.final_anomaly_summary(core_chain, rtk_start, rtk_end)
        
        print(f"   分裂事件: {len(anomalies['split_events'])}")
        print(f"   ID切换: {len(anomalies['id_switches'])}")
        print(f"   漏检间隙: {len(anomalies['missing_gaps'])}")
        print(f"   拒绝段: {len(anomalies['rejected_segments'])}")
        
        for event in anomalies['split_events']:
            print(f"      分裂: {event['timestamp']} IDs={event['ids']} 分数={event['score']:.3f}")
        
        for event in anomalies['id_switches']:
            print(f"      切换: {event['timestamp']} {event['from_id']}→{event['to_id']} 间隔={event['gap_duration']:.1f}s")
        
        for gap in anomalies['missing_gaps']:
            duration_seconds = gap['duration']
            if hasattr(gap['duration'], 'total_seconds'):
                duration_seconds = gap['duration'].total_seconds()
            print(f"      漏检: {gap['start_time']} ~ {gap['end_time']} 时长={duration_seconds:.1f}s 类型={gap['type']}")
        
        # 5. RTK轨迹剪裁
        print("5. RTK轨迹剪裁...")
        trimmed_rtk = trim_rtk_by_match_time(rtk_points, core_chain, config.rtk_buffer)
        print(f"   剪裁后RTK点数: {len(trimmed_rtk)}")
        
        # 6. 输出生成
        print("6. 输出生成...")
        output_generator = OutputGenerator(config)

        # 生成输出文件路径
        output_dir = os.path.join(temp_dir, 'output')
        os.makedirs(output_dir, exist_ok=True)

        matched_csv_path = os.path.join(output_dir, 'test_trajectory_matched.csv')
        diagnostic_json_path = os.path.join(output_dir, 'test_diagnostic.json')

        # 生成匹配CSV
        output_generator.generate_matched_csv(
            trimmed_rtk, core_chain, matched_csv_path, anomalies
        )
        print(f"   匹配CSV: {matched_csv_path}")
        
        # 生成诊断JSON
        output_generator.generate_diagnostic_json(
            anomalies, diagnostic_json_path, core_chain, trimmed_rtk
        )
        print(f"   诊断JSON: {diagnostic_json_path}")
        
        # 8. 验证输出文件
        print("8. 验证输出文件...")
        
        # 检查CSV文件
        if os.path.exists(matched_csv_path):
            csv_df = pd.read_csv(matched_csv_path)
            print(f"   CSV文件行数: {len(csv_df)}")
            print(f"   CSV文件列数: {len(csv_df.columns)}")
            print(f"   CSV文件列名: {list(csv_df.columns)}")
        
        # 检查JSON文件
        if os.path.exists(diagnostic_json_path):
            import json
            with open(diagnostic_json_path, 'r', encoding='utf-8') as f:
                diagnostic_data = json.load(f)
            print(f"   JSON文件键: {list(diagnostic_data.keys())}")
            print(f"   匹配段数: {diagnostic_data['metadata']['matched_segments_count']}")
            print(f"   覆盖率: {diagnostic_data['statistics']['coverage_rate']}%")
        
        print("\n✅ 完整功能测试通过！")
        
        # 显示输出文件路径供用户查看
        print(f"\n📁 输出文件位置:")
        print(f"   匹配CSV: {matched_csv_path}")
        print(f"   诊断JSON: {diagnostic_json_path}")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    finally:
        # 清理临时文件（可选）
        # shutil.rmtree(temp_dir)
        print(f"\n🗂️  临时文件保留在: {temp_dir}")

if __name__ == "__main__":
    success = test_complete_pipeline()
    sys.exit(0 if success else 1) 